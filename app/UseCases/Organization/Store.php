<?php

namespace App\UseCases\Organization;

use App\Domains\Organization;
use App\Factories\OrganizationFactory;
use App\Http\Requests\Organization\StoreRequest;
use App\Repositories\OrganizationRepository;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Services\ASAAS\UseCases\Deprecated\Organizations\CreateSubaccount;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class Store
{
    private OrganizationRepository $organizationRepository;
    private OrganizationFactory $organizationFactory;
    private CreateSubaccount $createSubaccount;

    public function __construct(
        OrganizationRepository $organizationRepository,
        OrganizationFactory $organizationFactory,
        CreateSubaccount $createSubaccount
    ) {
        $this->organizationRepository = $organizationRepository;
        $this->organizationFactory = $organizationFactory;
        $this->createSubaccount = $createSubaccount;
    }

    /**
     * @param Organization $organization
     * @param array $options
     * @return Organization
     */
    public function perform(Organization $organization, array $options = []) : Organization {
        DB::beginTransaction();

        try {
            // Store the organization first
            $this->organizationRepository->store($organization);

            // Try to create ASAAS subaccount if enabled and data is sufficient
            $createAsaasSubaccount = $options['create_asaas_subaccount'] ?? true;

            if ($createAsaasSubaccount && $this->createSubaccount->canCreateSubaccount($organization)) {
                try {
                    $asaasResult = $this->createSubaccount->perform($organization);

                    Log::info('ASAAS subaccount created during organization creation', [
                        'organization_id' => $organization->id,
                        'asaas_account_id' => $asaasResult['asaas_response']['id'] ?? null
                    ]);

                } catch (AsaasException $e) {
                    // Log the error but don't fail the organization creation
                    Log::warning('Failed to create ASAAS subaccount during organization creation', [
                        'organization_id' => $organization->id,
                        'error' => $e->getMessage(),
                        'asaas_error_code' => $e->getAsaasErrorCode()
                    ]);

                    // Optionally, you could set a flag to retry later
                    // $organization->needs_asaas_setup = true;
                    // $this->organizationRepository->store($organization);
                }
            }

            DB::commit();

            return $organization;

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to create organization', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }
}
