<?php

namespace App\UseCases\Inventory\Client;

use App\Domains\Inventory\Client;
use App\Factories\Inventory\ClientFactory;
use App\Http\Requests\Client\StoreRequest;
use App\Repositories\ClientRepository;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Services\ASAAS\UseCases\Deprecated\Clients\CreateCustomer;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class Store
{
    private ClientRepository $clientRepository;
    private ClientFactory $clientFactory;
    private CreateCustomer $createCustomer;

    public function __construct(
        ClientRepository $clientRepository,
        ClientFactory $clientFactory,
        CreateCustomer $createCustomer
    ) {
        $this->clientRepository = $clientRepository;
        $this->clientFactory = $clientFactory;
        $this->createCustomer = $createCustomer;
    }

    /**
     * @param StoreRequest $request
     * @return Client
     */
    public function perform(StoreRequest $request) : Client {
        DB::beginTransaction();

        try {
            $domain = $this->clientFactory->buildFromStoreRequest($request);
            $domain->organization_id = request()->user()->organization_id;

            $client = $this->clientRepository->store($domain);

            // Check if ASAAS integration should be created
            if ($request->input('enable_asaas_integration', false)) {
                try {
                    $this->createCustomer->perform($client);
                    Log::info('ASAAS customer created automatically', [
                        'client_id' => $client->id,
                        'organization_id' => $client->organization_id
                    ]);
                } catch (AsaasException $e) {
                    Log::warning('Failed to create ASAAS customer automatically', [
                        'client_id' => $client->id,
                        'organization_id' => $client->organization_id,
                        'error' => $e->getMessage()
                    ]);
                    // Don't fail the client creation if ASAAS fails
                }
            }

            DB::commit();
            return $client;

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}
