<?php

namespace App\UseCases\Inventory\Sale;

use App\Domains\Inventory\Sale;
use App\Factories\Inventory\SaleFactory;
use App\Http\Requests\Sale\StoreRequest;
use App\Repositories\SaleRepository;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Services\ASAAS\UseCases\Deprecated\Sales\CreatePayment;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class Store
{
    private SaleRepository $saleRepository;
    private SaleFactory $saleFactory;
    private CreatePayment $createPayment;

    public function __construct(
        SaleRepository $saleRepository,
        SaleFactory $saleFactory,
        CreatePayment $createPayment
    ) {
        $this->saleRepository = $saleRepository;
        $this->saleFactory = $saleFactory;
        $this->createPayment = $createPayment;
    }

    /**
     * @param StoreRequest $request
     * @return Sale
     */
    public function perform(StoreRequest $request) : Sale {
        DB::beginTransaction();

        try {
            $domain = $this->saleFactory->buildFromStoreRequest($request);
            $domain->organization_id = request()->user()->organization_id;

            $sale = $this->saleRepository->store($domain);

            // Check if ASAAS integration should be created
            if ($request->input('enable_asaas_integration', false)) {
                try {
                    $this->createPayment->perform($sale);
                    Log::info('ASAAS payment created automatically', [
                        'sale_id' => $sale->id,
                        'organization_id' => $sale->organization_id
                    ]);
                } catch (AsaasException $e) {
                    Log::warning('Failed to create ASAAS payment automatically', [
                        'sale_id' => $sale->id,
                        'organization_id' => $sale->organization_id,
                        'error' => $e->getMessage()
                    ]);
                    // Don't fail the sale creation if ASAAS fails
                }
            }

            DB::commit();
            return $sale;

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}
