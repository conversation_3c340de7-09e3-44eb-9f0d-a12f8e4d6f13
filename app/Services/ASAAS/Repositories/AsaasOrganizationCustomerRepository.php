<?php

namespace App\Services\ASAAS\Repositories;

use App\Services\ASAAS\Domains\AsaasOrganizationCustomer;
use App\Services\ASAAS\Factories\AsaasOrganizationCustomerFactory;
use App\Services\ASAAS\Models\AsaasOrganizationCustomer as AsaasOrganizationCustomerModel;
use Illuminate\Support\Collection;

class AsaasOrganizationCustomerRepository
{
    public function __construct(
        private AsaasOrganizationCustomerFactory $factory
    ) {}

    /**
     * Find AsaasOrganizationCustomer by ID
     */
    public function findById(int $id): ?AsaasOrganizationCustomer
    {
        $model = AsaasOrganizationCustomerModel::with(['organization'])
            ->find($id);
        return $this->factory->buildFromModel($model);
    }

    /**
     * Find AsaasOrganizationCustomer by organization ID
     */
    public function findByOrganizationId(int $organizationId): ?AsaasOrganizationCustomer
    {
        $model = AsaasOrganizationCustomerModel::with(['organization'])
            ->where('organization_id', $organizationId)
            ->first();
        return $this->factory->buildFromModel($model);
    }

    /**
     * Find AsaasOrganizationCustomer by ASAAS customer ID
     */
    public function findByAsaasCustomerId(string $asaasCustomerId): ?AsaasOrganizationCustomer
    {
        $model = AsaasOrganizationCustomerModel::with(['organization'])
            ->where('asaas_customer_id', $asaasCustomerId)
            ->first();
        return $this->factory->buildFromModel($model);
    }

    /**
     * Find AsaasOrganizationCustomer by external reference
     */
    public function findByExternalReference(string $externalReference): ?AsaasOrganizationCustomer
    {
        $model = AsaasOrganizationCustomerModel::with(['organization'])
            ->where('external_reference', $externalReference)
            ->first();
        return $this->factory->buildFromModel($model);
    }

    /**
     * Find AsaasOrganizationCustomer by email
     */
    public function findByEmail(string $email): ?AsaasOrganizationCustomer
    {
        $model = AsaasOrganizationCustomerModel::with(['organization'])
            ->where('email', $email)
            ->first();
        return $this->factory->buildFromModel($model);
    }

    /**
     * Find AsaasOrganizationCustomer by CPF/CNPJ
     */
    public function findByCpfCnpj(string $cpfCnpj): ?AsaasOrganizationCustomer
    {
        $model = AsaasOrganizationCustomerModel::with(['organization'])
            ->where('cpf_cnpj', $cpfCnpj)
            ->first();
        return $this->factory->buildFromModel($model);
    }

    /**
     * Get all AsaasOrganizationCustomers
     */
    public function getAll(int $limit = 100, int $offset = 0): Collection
    {
        $models = AsaasOrganizationCustomerModel::with(['organization'])
            ->limit($limit)
            ->offset($offset)
            ->get();
        return collect($this->factory->buildFromModels($models));
    }

    /**
     * Find customers that need synchronization
     */
    public function findPendingSync(int $limit = 100): Collection
    {
        $models = AsaasOrganizationCustomerModel::with(['organization'])
            ->needsSync()
            ->limit($limit)
            ->get();
        return collect($this->factory->buildFromModels($models));
    }

    /**
     * Find customers with sync errors
     */
    public function findWithErrors(int $limit = 100): Collection
    {
        $models = AsaasOrganizationCustomerModel::with(['organization'])
            ->withErrors()
            ->limit($limit)
            ->get();
        return collect($this->factory->buildFromModels($models));
    }

    /**
     * Find customers that need sync (pending or old sync)
     */
    public function findNeedingSync(int $limit = 100): Collection
    {
        $models = AsaasOrganizationCustomerModel::with(['organization'])
            ->where(function ($query) {
                $query->whereNull('asaas_synced_at')
                      ->orWhere('asaas_synced_at', '<', now()->subHour())
                      ->orWhere('sync_status', 'pending')
                      ->orWhere('sync_status', 'error');
            })
            ->limit($limit)
            ->get();
        return collect($this->factory->buildFromModels($models));
    }

    /**
     * Find customers by sync status
     */
    public function findBySyncStatus(string $status, int $limit = 100): Collection
    {
        $models = AsaasOrganizationCustomerModel::with(['organization'])
            ->where('sync_status', $status)
            ->limit($limit)
            ->get();
        return collect($this->factory->buildFromModels($models));
    }

    /**
     * Save AsaasOrganizationCustomer (create or update)
     */
    public function save(AsaasOrganizationCustomer $customer): AsaasOrganizationCustomer
    {
        if ($customer->id) {
            return $this->update($customer);
        } else {
            return $this->create($customer);
        }
    }

    /**
     * Create new AsaasOrganizationCustomer
     */
    public function store(AsaasOrganizationCustomer $customer): AsaasOrganizationCustomer
    {
        $model = AsaasOrganizationCustomerModel::create($customer->toStoreArray());
        $model->load(['organization']);
        return $this->factory->buildFromModel($model);
    }

    /**
     * Update AsaasOrganizationCustomer
     */
    public function update(AsaasOrganizationCustomer $customer): AsaasOrganizationCustomer
    {
        $model = AsaasOrganizationCustomerModel::findOrFail($customer->id);
        $model->update($customer->toUpdateArray());
        $model->load(['organization']);
        return $this->factory->buildFromModel($model);
    }

    /**
     * Delete AsaasOrganizationCustomer
     */
    public function delete(int $id): bool
    {
        $model = AsaasOrganizationCustomerModel::findOrFail($id);
        return $model->delete();
    }

    /**
     * Soft delete AsaasOrganizationCustomer
     */
    public function softDelete(int $id): bool
    {
        $model = AsaasOrganizationCustomerModel::findOrFail($id);
        return $model->delete();
    }

    /**
     * Restore soft deleted AsaasOrganizationCustomer
     */
    public function restore(int $id): bool
    {
        $model = AsaasOrganizationCustomerModel::withTrashed()->findOrFail($id);
        return $model->restore();
    }

    /**
     * Force delete AsaasOrganizationCustomer
     */
    public function forceDelete(int $id): bool
    {
        $model = AsaasOrganizationCustomerModel::withTrashed()->findOrFail($id);
        return $model->forceDelete();
    }

    /**
     * Check if organization already has a customer
     */
    public function organizationHasCustomer(int $organizationId): bool
    {
        return AsaasOrganizationCustomerModel::where('organization_id', $organizationId)->exists();
    }

    /**
     * Check if ASAAS customer ID already exists
     */
    public function asaasCustomerIdExists(string $asaasCustomerId): bool
    {
        return AsaasOrganizationCustomerModel::where('asaas_customer_id', $asaasCustomerId)->exists();
    }

    /**
     * Count customers by sync status
     */
    public function countBySyncStatus(string $status): int
    {
        return AsaasOrganizationCustomerModel::where('sync_status', $status)->count();
    }

    /**
     * Count total customers
     */
    public function count(): int
    {
        return AsaasOrganizationCustomerModel::count();
    }

    /**
     * Count customers that need sync
     */
    public function countNeedingSync(): int
    {
        return AsaasOrganizationCustomerModel::needsSync()->count();
    }

    /**
     * Count customers with errors
     */
    public function countWithErrors(): int
    {
        return AsaasOrganizationCustomerModel::withErrors()->count();
    }

    /**
     * Get customers with filters
     */
    public function findWithFilters(array $filters = [], int $limit = 100): Collection
    {
        $query = AsaasOrganizationCustomerModel::with(['organization']);

        if (isset($filters['organization_id'])) {
            $query->where('organization_id', $filters['organization_id']);
        }

        if (isset($filters['sync_status'])) {
            $query->where('sync_status', $filters['sync_status']);
        }

        if (isset($filters['has_errors'])) {
            if ($filters['has_errors']) {
                $query->whereNotNull('asaas_sync_errors');
            } else {
                $query->whereNull('asaas_sync_errors');
            }
        }

        if (isset($filters['needs_sync'])) {
            if ($filters['needs_sync']) {
                $query->where(function ($q) {
                    $q->whereNull('asaas_synced_at')
                      ->orWhere('asaas_synced_at', '<', now()->subHour());
                });
            }
        }

        if (isset($filters['person_type'])) {
            $query->where('person_type', $filters['person_type']);
        }

        if (isset($filters['email'])) {
            $query->where('email', 'like', "%{$filters['email']}%");
        }

        if (isset($filters['name'])) {
            $query->where('name', 'like', "%{$filters['name']}%");
        }

        if (isset($filters['cpf_cnpj'])) {
            $query->where('cpf_cnpj', 'like', "%{$filters['cpf_cnpj']}%");
        }

        $models = $query->limit($limit)->get();
        return collect($this->factory->buildFromModels($models));
    }

    /**
     * Bulk update ASAAS customer IDs
     */
    public function bulkUpdateAsaasCustomerIds(array $updates): bool
    {
        foreach ($updates as $update) {
            AsaasOrganizationCustomerModel::where('id', $update['id'])
                ->update(['asaas_customer_id' => $update['asaas_customer_id']]);
        }
        return true;
    }

    /**
     * Bulk update sync status
     */
    public function bulkUpdateSyncStatus(array $ids, string $status, ?array $errors = null): bool
    {
        $updateData = [
            'sync_status' => $status,
            'asaas_synced_at' => $status === 'synced' ? now() : null,
        ];

        if ($errors !== null) {
            $updateData['asaas_sync_errors'] = $errors;
        }

        AsaasOrganizationCustomerModel::whereIn('id', $ids)->update($updateData);
        return true;
    }
}
