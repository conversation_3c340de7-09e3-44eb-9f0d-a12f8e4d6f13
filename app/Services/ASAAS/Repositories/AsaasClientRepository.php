<?php

namespace App\Services\ASAAS\Repositories;

use App\Services\ASAAS\Domains\AsaasClient;
use App\Services\ASAAS\Models\AsaasClient as AsaasClientModel;
use App\Services\ASAAS\Factories\AsaasClientFactory;
use Illuminate\Support\Collection;

class AsaasClientRepository
{
    public function __construct(
        private AsaasClientFactory $factory
    ) {}

    /**
     * Find AsaasClient by ID
     */
    public function findById(int $id): ?AsaasClient
    {
        $model = AsaasClientModel::with(['client'])->find($id);
        return $this->factory->buildFromModel($model);
    }

    /**
     * Find AsaasClient by client ID
     */
    public function findByClientId(int $clientId): ?AsaasClient
    {
        $model = AsaasClientModel::with(['client'])
            ->where('client_id', $clientId)
            ->first();
        return $this->factory->buildFromModel($model);
    }

    /**
     * Find AsaasClient by ASAAS customer ID
     */
    public function findByAsaasCustomerId(string $asaasCustomerId): ?AsaasClient
    {
        $model = AsaasClientModel::with(['client'])
            ->where('asaas_customer_id', $asaasCustomerId)
            ->first();
        return $this->factory->buildFromModel($model);
    }

    /**
     * Find AsaasClient by external reference
     */
    public function findByExternalReference(string $externalReference): ?AsaasClient
    {
        $model = AsaasClientModel::with(['client'])
            ->where('asaas_external_reference', $externalReference)
            ->first();
        return $this->factory->buildFromModel($model);
    }

    /**
     * Get AsaasClients by organization ID
     */
    public function getByOrganizationId(int $organizationId): Collection
    {
        $models = AsaasClientModel::with(['client'])
            ->where('organization_id', $organizationId)
            ->get();

        return collect($this->factory->buildFromModels($models));
    }

    /**
     * Get all active AsaasClients
     */
    public function getAllActive(): Collection
    {
        $models = AsaasClientModel::with(['client'])
            ->where('is_active', true)
            ->get();

        return collect($this->factory->buildFromModels($models));
    }

    /**
     * Get AsaasClients that need sync
     */
    public function getNeedingSync(): Collection
    {
        $models = AsaasClientModel::with(['client'])
            ->where('is_active', true)
            ->where(function ($query) {
                $query->whereNull('asaas_synced_at')
                      ->orWhere('asaas_synced_at', '<', now()->subHour());
            })
            ->get();

        return collect($this->factory->buildFromModels($models));
    }

    /**
     * Get AsaasClients with sync errors
     */
    public function getWithSyncErrors(): Collection
    {
        $models = AsaasClientModel::with(['client'])
            ->whereNotNull('asaas_sync_errors')
            ->get();

        return collect($this->factory->buildFromModels($models));
    }

    /**
     * Get AsaasClients with high sync attempts
     */
    public function getWithHighSyncAttempts(int $threshold = 5): Collection
    {
        $models = AsaasClientModel::with(['client'])
            ->where('sync_attempts', '>=', $threshold)
            ->get();

        return collect($this->factory->buildFromModels($models));
    }

    /**
     * Get AsaasClients without ASAAS customer ID
     */
    public function getWithoutAsaasCustomerId(): Collection
    {
        $models = AsaasClientModel::with(['client'])
            ->whereNull('asaas_customer_id')
            ->where('is_active', true)
            ->get();

        return collect($this->factory->buildFromModels($models));
    }

    /**
     * Save AsaasClient (create or update)
     */
    public function save(AsaasClient $asaasClient): AsaasClient
    {
        if ($asaasClient->id) {
            return $this->update($asaasClient);
        } else {
            return $this->store($asaasClient);
        }
    }

    /**
     * Create new AsaasClient
     */
    public function store(AsaasClient $asaasClient): AsaasClient
    {
        $model = AsaasClientModel::create($asaasClient->toStoreArray());
        $model->load(['client']);
        return $this->factory->buildFromModel($model);
    }

    public function create(AsaasClient $asaasClient): AsaasClient
    {
        return $this->store($asaasClient);
    }
    /**
     * Update AsaasClient
     */
    public function update(AsaasClient $asaasClient): AsaasClient
    {
        $model = AsaasClientModel::findOrFail($asaasClient->id);
        $model->update($asaasClient->toUpdateArray());
        $model->load(['client']);
        return $this->factory->buildFromModel($model);
    }

    /**
     * Find AsaasClients by organization
     */
    public function findByOrganization(int $organizationId): array
    {
        $models = AsaasClientModel::with(['client'])
            ->where('organization_id', $organizationId)
            ->get();

        return $this->factory->buildFromModels($models);
    }

    /**
     * Find AsaasClients needing sync
     */
    public function findNeedingSync(): array
    {
        $models = AsaasClientModel::with(['client'])
            ->where(function ($query) {
                $query->whereNull('asaas_synced_at')
                      ->orWhere('asaas_synced_at', '<', now()->subHour());
            })
            ->get();

        return $this->factory->buildFromModels($models);
    }

    /**
     * Find AsaasClients with sync errors
     */
    public function findWithSyncErrors(): array
    {
        $models = AsaasClientModel::with(['client'])
            ->whereNotNull('asaas_sync_errors')
            ->get();

        return $this->factory->buildFromModels($models);
    }

    /**
     * Delete AsaasClient by ID
     */
    public function deleteById(int $id): bool
    {
        return AsaasClientModel::destroy($id) > 0;
    }

    /**
     * Delete AsaasClient
     */
    public function delete(int $id): bool
    {
        return AsaasClientModel::destroy($id) > 0;
    }

    /**
     * Mark as synced
     */
    public function markAsSynced(int $id, ?array $response = null): ?AsaasClient
    {
        $updated = AsaasClientModel::where('id', $id)->update([
            'asaas_synced_at' => now(),
            'asaas_sync_errors' => null,
        ]);

        if ($updated > 0) {
            return $this->findById($id);
        }

        return null;
    }

    /**
     * Mark sync error
     */
    public function markSyncError(int $id, array $errorData): ?AsaasClient
    {
        $updated = AsaasClientModel::where('id', $id)->update([
            'asaas_sync_errors' => $errorData,
        ]);

        if ($updated > 0) {
            return $this->findById($id);
        }

        return null;
    }

    /**
     * Add sync error
     */
    public function addSyncError(int $id, string $error): bool
    {
        $model = AsaasClientModel::find($id);
        if (!$model) {
            return false;
        }

        $errors = $model->asaas_sync_errors ?? [];
        $errors[] = [
            'error' => $error,
            'timestamp' => now()->toISOString(),
        ];

        // Keep only last 5 errors
        if (count($errors) > 5) {
            $errors = array_slice($errors, -5);
        }

        return $model->update([
            'asaas_sync_errors' => $errors,
            'sync_attempts' => $model->sync_attempts + 1,
        ]);
    }

    /**
     * Reset sync attempts
     */
    public function resetSyncAttempts(int $id): bool
    {
        return AsaasClientModel::where('id', $id)->update([
            'sync_attempts' => 0,
            'asaas_sync_errors' => null,
        ]) > 0;
    }

    /**
     * Deactivate AsaasClient
     */
    public function deactivate(int $id, string $reason): bool
    {
        return AsaasClientModel::where('id', $id)->update([
            'is_active' => false,
            'deactivated_at' => now(),
            'deactivation_reason' => $reason,
        ]) > 0;
    }

    /**
     * Reactivate AsaasClient
     */
    public function reactivate(int $id): bool
    {
        return AsaasClientModel::where('id', $id)->update([
            'is_active' => true,
            'deactivated_at' => null,
            'deactivation_reason' => null,
        ]) > 0;
    }

    /**
     * Check if client exists by client_id
     */
    public function existsByClientId(int $clientId): bool
    {
        return AsaasClientModel::where('client_id', $clientId)->exists();
    }

    /**
     * Check if ASAAS customer ID is already in use
     */
    public function existsByAsaasCustomerId(string $asaasCustomerId): bool
    {
        return AsaasClientModel::where('asaas_customer_id', $asaasCustomerId)->exists();
    }

    /**
     * Get count by organization
     */
    public function countByOrganizationId(int $organizationId): int
    {
        return AsaasClientModel::where('organization_id', $organizationId)->count();
    }

    /**
     * Get count of active clients
     */
    public function countActive(): int
    {
        return AsaasClientModel::where('is_active', true)->count();
    }

    /**
     * Get count of clients with ASAAS integration
     */
    public function countWithAsaasIntegration(): int
    {
        return AsaasClientModel::whereNotNull('asaas_customer_id')->count();
    }

    /**
     * Search AsaasClients
     */
    public function search(array $filters = [], int $limit = 50): Collection
    {
        $query = AsaasClientModel::with(['client']);

        if (isset($filters['organization_id'])) {
            $query->where('organization_id', $filters['organization_id']);
        }

        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        if (isset($filters['has_asaas_customer_id'])) {
            if ($filters['has_asaas_customer_id']) {
                $query->whereNotNull('asaas_customer_id');
            } else {
                $query->whereNull('asaas_customer_id');
            }
        }

        if (isset($filters['has_sync_errors'])) {
            if ($filters['has_sync_errors']) {
                $query->whereNotNull('asaas_sync_errors');
            } else {
                $query->whereNull('asaas_sync_errors');
            }
        }

        if (isset($filters['needs_sync'])) {
            if ($filters['needs_sync']) {
                $query->where(function ($q) {
                    $q->whereNull('asaas_synced_at')
                      ->orWhere('asaas_synced_at', '<', now()->subHour());
                });
            }
        }

        if (isset($filters['sync_attempts_gte'])) {
            $query->where('sync_attempts', '>=', $filters['sync_attempts_gte']);
        }

        $models = $query->limit($limit)->get();
        return collect($this->factory->buildFromModels($models));
    }

    /**
     * Bulk update ASAAS customer IDs
     */
    public function bulkUpdateAsaasCustomerIds(array $updates): bool
    {
        foreach ($updates as $update) {
            AsaasClientModel::where('id', $update['id'])
                ->update(['asaas_customer_id' => $update['asaas_customer_id']]);
        }
        return true;
    }
}
