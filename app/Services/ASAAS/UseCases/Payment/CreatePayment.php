<?php

namespace App\Services\ASAAS\UseCases\Payment;

use App\Domains\Inventory\Sale;
use App\Services\ASAAS\PaymentService;
use App\Services\ASAAS\Exceptions\AsaasException;

class CreatePayment
{
    private PaymentService $paymentService;

    public function __construct(PaymentService $paymentService)
    {
        $this->paymentService = $paymentService;
    }

    public function perform(Sale $sale): array
    {
        try {
            return $this->paymentService->create($sale->toAsaasPayload());
        } catch (\Exception $e) {
            throw new AsaasException("Failed to create payment: {$e->getMessage()}", $e->getCode(), $e);
        }
    }
}
