<?php

namespace App\Services\ASAAS\UseCases\Customer;

use App\Domains\Organization;
use App\Services\ASAAS\CustomerService;
use App\Services\ASAAS\Exceptions\AsaasException;
use Throwable;

class GetAllCustomers
{
    private CustomerService $customerService;

    public function __construct(CustomerService $customerService)
    {
        $this->customerService = $customerService;
    }

    /**
     * @param array $filters
     * @param Organization|null $organization
     * @return array
     * @throws AsaasException
     */
    public function perform(array $filters = [], ?Organization $organization = null): array
    {
        try {
            if ($organization) {
                $this->customerService = new CustomerService($organization);
            }
            return $this->customerService->getAll($filters);
        } catch (Throwable $e) {
            throw new AsaasException("Failed to get customers: {$e->getMessage()}", $e->getCode(), $e);
        }
    }
}
