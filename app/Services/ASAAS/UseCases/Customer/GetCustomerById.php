<?php

namespace App\Services\ASAAS\UseCases\Customer;

use App\Services\ASAAS\CustomerService;
use App\Services\ASAAS\Exceptions\AsaasException;

class GetCustomerById
{
    private CustomerService $customerService;

    public function __construct(CustomerService $customerService)
    {
        $this->customerService = $customerService;
    }

    public function perform(string $customer_id): array
    {
        try {
            return $this->customerService->getById($customer_id);
        } catch (\Exception $e) {
            throw new AsaasException("Failed to get customer: {$e->getMessage()}", $e->getCode(), $e);
        }
    }
}
