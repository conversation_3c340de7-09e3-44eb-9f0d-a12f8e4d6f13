<?php

namespace App\Services\ASAAS\UseCases\Customer;

use App\Domains\Inventory\Client;
use App\Services\ASAAS\CustomerService;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Services\ASAAS\Factories\AsaasClientFactory;
use App\Services\ASAAS\Repositories\AsaasClientRepository;
use App\Services\ASAAS\Models\AsaasLog;
use App\UseCases\Organization\Get as GetOrganization;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CreateCustomerClient
{
    private CustomerService $customerService;
    private AsaasClientFactory $asaasClientFactory;
    private AsaasClientRepository $asaasClientRepository;


    public function __construct(
        AsaasClientFactory $asaasClientFactory,
        AsaasClientRepository $asaasClientRepository,
    ) {
        $this->asaasClientFactory = $asaasClientFactory;
        $this->asaasClientRepository = $asaasClientRepository;
    }

    /**
     * Create ASAAS customer for client using organization credentials
     *
     * @param Client $client
     * @return array
     * @throws AsaasException
     */
    public function perform(Client $client): array
    {
        if ($client->hasAsaasIntegration()) {
            throw new AsaasException('Client already has an ASAAS customer integration');
        }

        if (!$client->organization->asaas_api_key) {
            throw new AsaasException('Organization does not have ASAAS API key configured');
        }

        DB::beginTransaction();

        try {
            $this->customerService = new CustomerService($client->organization);

            $asaasResponse = $this->customerService->create(
                $client->toAsaasCustomerPayload()
            );

            $asaasClient = $this->asaasClientFactory->buildFromAsaasResponse(
                $asaasResponse,
                $client->id,
                $client->organization_id
            );

            $this->asaasClientRepository->store($asaasClient);

            DB::commit();

            Log::info('ASAAS customer created successfully for client', [
                'client_id' => $client->id,
                'organization_id' => $client->organization_id,
                'asaas_customer_id' => $asaasResponse['id'] ?? null
            ]);

            return [
                'status' => 'success',
                'message' => 'Customer created successfully',
                'asaas_customer_id' => $asaasResponse['id'] ?? null,
                'customer_data' => $asaasClient->toArray()
            ];

        } catch (\Throwable $e) {
            DB::rollBack();

            Log::error('Failed to create ASAAS customer for client', [
                'client_id' => $client->id,
                'organization_id' => $client->organization_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            if ($e instanceof AsaasException) {
                throw $e;
            }

            throw new AsaasException(
                'Failed to create customer: ' . $e->getMessage(),
                0,
                $e
            );
        }
    }
}
