<?php

namespace App\Services\ASAAS\UseCases\Customer;

use App\Services\ASAAS\CustomerService;
use App\Services\ASAAS\Exceptions\AsaasException;

class UpdateCustomer
{
    private CustomerService $customerService;

    public function __construct(CustomerService $customerService)
    {
        $this->customerService = $customerService;
    }

    public function perform(string $customer_id, array $data): array
    {
        try {
            return $this->customerService->update($customer_id, $data);
        } catch (\Exception $e) {
            throw new AsaasException("Failed to update customer: {$e->getMessage()}", $e->getCode(), $e);
        }
    }
}
