<?php

namespace App\Services\ASAAS\UseCases\Customer;

use App\Services\ASAAS\CustomerService;
use App\Services\ASAAS\Exceptions\AsaasException;
use Throwable;

class DeleteCustomer
{
    private CustomerService $customerService;

    public function __construct(CustomerService $customerService)
    {
        $this->customerService = $customerService;
    }

    public function perform(string $customer_id): array
    {
        try {
            return $this->customerService->deleteCustomer($customer_id);
        } catch (Throwable $e) {
            throw new AsaasException("Failed to delete customer: {$e->getMessage()}", $e->getCode(), $e);
        }
    }
}
