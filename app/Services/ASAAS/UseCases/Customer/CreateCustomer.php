<?php

namespace App\Services\ASAAS\UseCases\Customer;

use App\Services\ASAAS\CustomerService;
use App\Services\ASAAS\Exceptions\AsaasException;
use Throwable;

class CreateCustomer
{
    private CustomerService $customerService;

    public function __construct(CustomerService $customerService)
    {
        $this->customerService = $customerService;
    }

    /**
     * @throws AsaasException
     */
    public function perform(array $data): array
    {
        try {
            return $this->customerService->create($data);
        } catch (Throwable $e) {
            throw new AsaasException("Failed to create customer: {$e->getMessage()}", $e->getCode(), $e);
        }
    }
}
