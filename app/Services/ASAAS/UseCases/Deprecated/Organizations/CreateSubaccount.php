<?php

namespace App\Services\ASAAS\UseCases\Deprecated\Organizations;

use App\Domains\Organization;
use App\Enums\AsaasEnvironment;
use App\Helpers\DBLog;
use App\Services\ASAAS\AsaasService;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Services\ASAAS\Factories\AsaasOrganizationFactory;
use App\Services\ASAAS\Repositories\AsaasOrganizationRepository;
use Exception;
use Illuminate\Support\Facades\DB;

/**
 * Creates ASAAS subaccounts for organizations.
 * Handles account creation, API key management, and local storage of organization data.
 */
class CreateSubaccount
{
    protected AsaasService $asaasService;
    protected AsaasOrganizationRepository $asaasOrganizationRepository;
    protected AsaasOrganizationFactory $asaasOrganizationFactory;
    protected StoreAsaasOrganization $storeAsaasOrganization;

    public function __construct(
        AsaasService $asaasService,
        AsaasOrganizationRepository $asaasOrganizationRepository,
        AsaasOrganizationFactory $asaasOrganizationFactory,
        StoreAsaasOrganization $storeAsaasOrganization
    ) {
        $this->asaasService = $asaasService;
        $this->asaasOrganizationRepository = $asaasOrganizationRepository;
        $this->asaasOrganizationFactory = $asaasOrganizationFactory;
        $this->storeAsaasOrganization = $storeAsaasOrganization;
    }

    /**
     * Create a subaccount in ASAAS for the organization
     *
     * @param Organization $organization
     * @param AsaasEnvironment|null $environment
     * @return array
     * @throws AsaasException
     */
    public function perform(Organization $organization, ?AsaasEnvironment $environment = null): array
    {
        if ($organization->hasAsaasIntegration()) {
            throw new AsaasException("Organization already has an ASAAS account");
        }
        $organization->validateAsaasPayload();

        $subaccountData = $organization->toAsaasPayload();

        DB::beginTransaction();

        try {
            DBLog::logInfo('Creating ASAAS subaccount', 'CreateSubaccount');
            $response = $this->asaasService->post(
                '/v3/accounts',
                $subaccountData,
                null, // Use default token
                $environment
            );

            // Create ASAAS organization record
            $asaasOrganization = $this->storeAsaasOrganization->performFromAsaasResponse(
                $response,
                $organization,
                $environment
            );

            DB::commit();

            DBLog::logInfo('ASAAS subaccount created successfully',
                'CreateSubaccount',
                $organization->id,
                null, [
                'organization_id' => $organization->id,
                'asaas_account_id' => $response['id'],
                'environment' => $environment?->value ?? config('asaas.environment')
            ]);

            return [
                'success' => true,
                'organization_id' => $organization->id,
                'asaas_organization' => $asaasOrganization,
                'asaas_response' => $response,
                'message' => 'Subaccount created successfully'
            ];

        } catch (AsaasException $e) {
            DB::rollBack();

            DBLog::logError(
                'Failed to create ASAAS subaccount',
                'CreateSubaccount',
                $organization->id ?? null,
                null, [
                    'organization_id' => $organization->id ?? null,
                    'error' => $e->getMessage(),
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );

            throw $e;

        } catch (Exception $e) {
            DB::rollBack();

            DBLog::logError('Unexpected error creating ASAAS subaccount',
                'CreateSubaccount',
                $organization->id ?? null,
                null, [
                'organization_id' => $organization->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw new AsaasException(
                'Failed to create subaccount: ' . $e->getMessage(),
                0,
                $e
            );
        }
    }

    /**
     * Check if organization can create subaccount
     *
     * @param Organization $organization
     * @return bool
     */
    public function canCreateSubaccount(Organization $organization): bool
    {
        // Already has subaccount
        if ($organization->hasAsaasIntegration()) {
            return false;
        }

        // Check required fields
        if (empty($organization->name)) {
            return false;
        }

        return true;
    }
}
