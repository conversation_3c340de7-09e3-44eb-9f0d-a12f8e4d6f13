<?php

namespace App\Services\ASAAS\UseCases\Deprecated\Organizations;

use App\Enums\AsaasEnvironment;
use App\Services\ASAAS\Domains\AsaasOrganization;
use App\Services\ASAAS\Factories\AsaasOrganizationFactory;
use App\Services\ASAAS\Repositories\AsaasOrganizationRepository;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * Updates ASAAS organization data and configuration.
 * Handles API key updates, environment changes, and organization settings.
 */
class UpdateAsaasOrganization
{
    private AsaasOrganizationRepository $asaasOrganizationRepository;
    private AsaasOrganizationFactory $asaasOrganizationFactory;

    public function __construct(
        AsaasOrganizationRepository $asaasOrganizationRepository,
        AsaasOrganizationFactory $asaasOrganizationFactory
    ) {
        $this->asaasOrganizationRepository = $asaasOrganizationRepository;
        $this->asaasOrganizationFactory = $asaasOrganizationFactory;
    }

    /**
     * Update an existing AsaasOrganization
     *
     * @param int $asaasOrganizationId
     * @param array $data
     * @return AsaasOrganization
     */
    public function perform(int $asaasOrganizationId, array $data): AsaasOrganization
    {
        // Find existing AsaasOrganization
        $existingAsaasOrganization = $this->asaasOrganizationRepository->findById($asaasOrganizationId);
        if (!$existingAsaasOrganization) {
            throw new \InvalidArgumentException("AsaasOrganization not found with ID: {$asaasOrganizationId}");
        }

        DB::beginTransaction();

        try {
            // Update domain properties
            $this->updateDomainProperties($existingAsaasOrganization, $data);

            // Update in database
            $updatedAsaasOrganization = $this->asaasOrganizationRepository->update($existingAsaasOrganization);

            DB::commit();

            Log::info('AsaasOrganization updated successfully', [
                'asaas_organization_id' => $asaasOrganizationId,
                'organization_id' => $updatedAsaasOrganization->organization_id,
                'updated_fields' => array_keys($data)
            ]);

            return $updatedAsaasOrganization;

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to update AsaasOrganization', [
                'asaas_organization_id' => $asaasOrganizationId,
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            throw $e;
        }
    }

    /**
     * Update AsaasOrganization from ASAAS API response
     *
     * @param int $asaasOrganizationId
     * @param array $asaasResponse
     * @return AsaasOrganization
     */
    public function performFromAsaasResponse(int $asaasOrganizationId, array $asaasResponse): AsaasOrganization
    {
        // Find existing AsaasOrganization
        $existingAsaasOrganization = $this->asaasOrganizationRepository->findById($asaasOrganizationId);
        if (!$existingAsaasOrganization) {
            throw new \InvalidArgumentException("AsaasOrganization not found with ID: {$asaasOrganizationId}");
        }

        DB::beginTransaction();

        try {
            // Update from ASAAS response
            $this->updateFromAsaasResponse($existingAsaasOrganization, $asaasResponse);

            // Update in database
            $updatedAsaasOrganization = $this->asaasOrganizationRepository->update($existingAsaasOrganization);

            DB::commit();

            Log::info('AsaasOrganization updated from ASAAS response', [
                'asaas_organization_id' => $asaasOrganizationId,
                'organization_id' => $updatedAsaasOrganization->organization_id,
                'asaas_account_id' => $updatedAsaasOrganization->asaas_account_id
            ]);

            return $updatedAsaasOrganization;

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to update AsaasOrganization from ASAAS response', [
                'asaas_organization_id' => $asaasOrganizationId,
                'error' => $e->getMessage(),
                'asaas_response' => $asaasResponse
            ]);

            throw $e;
        }
    }

    /**
     * Update domain properties from data array
     *
     * @param AsaasOrganization $asaasOrganization
     * @param array $data
     * @return void
     */
    private function updateDomainProperties(AsaasOrganization $asaasOrganization, array $data): void
    {
        if (isset($data['asaas_account_id'])) $asaasOrganization->asaas_account_id = $data['asaas_account_id'];
        if (isset($data['asaas_api_key'])) $asaasOrganization->asaas_api_key = $data['asaas_api_key'];
        if (isset($data['asaas_wallet_id'])) $asaasOrganization->asaas_wallet_id = $data['asaas_wallet_id'];
        if (isset($data['asaas_environment'])) $asaasOrganization->asaas_environment = AsaasEnvironment::from($data['asaas_environment']);
        if (isset($data['is_active'])) $asaasOrganization->is_active = $data['is_active'];
        if (isset($data['last_sync_at'])) $asaasOrganization->last_sync_at = $data['last_sync_at'];
        if (isset($data['sync_errors'])) $asaasOrganization->sync_errors = $data['sync_errors'];
        if (isset($data['name'])) $asaasOrganization->name = $data['name'];
        if (isset($data['email'])) $asaasOrganization->email = $data['email'];
        if (isset($data['login_email'])) $asaasOrganization->login_email = $data['login_email'];
        if (isset($data['phone'])) $asaasOrganization->phone = $data['phone'];
        if (isset($data['mobile_phone'])) $asaasOrganization->mobile_phone = $data['mobile_phone'];
        if (isset($data['address'])) $asaasOrganization->address = $data['address'];
        if (isset($data['address_number'])) $asaasOrganization->address_number = $data['address_number'];
        if (isset($data['complement'])) $asaasOrganization->complement = $data['complement'];
        if (isset($data['province'])) $asaasOrganization->province = $data['province'];
        if (isset($data['postal_code'])) $asaasOrganization->postal_code = $data['postal_code'];
        if (isset($data['cpf_cnpj'])) $asaasOrganization->cpf_cnpj = $data['cpf_cnpj'];
        if (isset($data['birth_date'])) $asaasOrganization->birth_date = $data['birth_date'];
        if (isset($data['person_type'])) $asaasOrganization->person_type = $data['person_type'];
        if (isset($data['company_type'])) $asaasOrganization->company_type = $data['company_type'];
        if (isset($data['city'])) $asaasOrganization->city = $data['city'];
        if (isset($data['state'])) $asaasOrganization->state = $data['state'];
        if (isset($data['country'])) $asaasOrganization->country = $data['country'];
        if (isset($data['trading_name'])) $asaasOrganization->trading_name = $data['trading_name'];
        if (isset($data['income_value'])) $asaasOrganization->income_value = $data['income_value'];
        if (isset($data['site'])) $asaasOrganization->site = $data['site'];
        if (isset($data['account_number'])) $asaasOrganization->account_number = $data['account_number'];
        if (isset($data['commercial_info_expiration'])) $asaasOrganization->commercial_info_expiration = $data['commercial_info_expiration'];
    }

    /**
     * Update domain properties from ASAAS response
     *
     * @param AsaasOrganization $asaasOrganization
     * @param array $asaasResponse
     * @return void
     */
    private function updateFromAsaasResponse(AsaasOrganization $asaasOrganization, array $asaasResponse): void
    {
        if (isset($asaasResponse['id'])) $asaasOrganization->asaas_account_id = $asaasResponse['id'];
        if (isset($asaasResponse['apiKey'])) $asaasOrganization->asaas_api_key = $asaasResponse['apiKey'];
        if (isset($asaasResponse['walletId'])) $asaasOrganization->asaas_wallet_id = $asaasResponse['walletId'];
        if (isset($asaasResponse['name'])) $asaasOrganization->name = $asaasResponse['name'];
        if (isset($asaasResponse['email'])) $asaasOrganization->email = $asaasResponse['email'];
        if (isset($asaasResponse['loginEmail'])) $asaasOrganization->login_email = $asaasResponse['loginEmail'];
        if (isset($asaasResponse['phone'])) $asaasOrganization->phone = $asaasResponse['phone'];
        if (isset($asaasResponse['mobilePhone'])) $asaasOrganization->mobile_phone = $asaasResponse['mobilePhone'];
        if (isset($asaasResponse['address'])) $asaasOrganization->address = $asaasResponse['address'];
        if (isset($asaasResponse['addressNumber'])) $asaasOrganization->address_number = $asaasResponse['addressNumber'];
        if (isset($asaasResponse['complement'])) $asaasOrganization->complement = $asaasResponse['complement'];
        if (isset($asaasResponse['province'])) $asaasOrganization->province = $asaasResponse['province'];
        if (isset($asaasResponse['postalCode'])) $asaasOrganization->postal_code = $asaasResponse['postalCode'];
        if (isset($asaasResponse['cpfCnpj'])) $asaasOrganization->cpf_cnpj = $asaasResponse['cpfCnpj'];
        if (isset($asaasResponse['birthDate'])) $asaasOrganization->birth_date = $asaasResponse['birthDate'];
        if (isset($asaasResponse['personType'])) $asaasOrganization->person_type = $asaasResponse['personType'];
        if (isset($asaasResponse['companyType'])) $asaasOrganization->company_type = $asaasResponse['companyType'];
        if (isset($asaasResponse['city'])) $asaasOrganization->city = $asaasResponse['city'];
        if (isset($asaasResponse['state'])) $asaasOrganization->state = $asaasResponse['state'];
        if (isset($asaasResponse['country'])) $asaasOrganization->country = $asaasResponse['country'];
        if (isset($asaasResponse['tradingName'])) $asaasOrganization->trading_name = $asaasResponse['tradingName'];
        if (isset($asaasResponse['site'])) $asaasOrganization->site = $asaasResponse['site'];
        if (isset($asaasResponse['accountNumber'])) $asaasOrganization->account_number = $asaasResponse['accountNumber'];
        if (isset($asaasResponse['commercialInfoExpiration'])) $asaasOrganization->commercial_info_expiration = $asaasResponse['commercialInfoExpiration'];

        // Update sync information
        $asaasOrganization->last_sync_at = now();
        $asaasOrganization->sync_errors = null;
    }
}
