<?php

namespace App\Services\ASAAS\UseCases\Deprecated\Organizations;

use App\Domains\Organization;
use App\Enums\AsaasEnvironment;
use App\Services\ASAAS\AsaasService;
use App\Services\ASAAS\Exceptions\AsaasException;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

/**
 * Checks and validates organization subscription status in ASAAS.
 * Handles subscription verification, payment status, and access control validation.
 */
class CheckSubscriptionStatus
{
    protected AsaasService $asaasService;

    public function __construct(AsaasService $asaasService)
    {
        $this->asaasService = $asaasService;
    }

    /**
     * Check and update subscription status from ASAAS
     *
     * @param Organization $organization
     * @return array
     */
    public function perform(Organization $organization): array
    {
        if (!$organization->hasAsaasIntegration()) {
            return [
                'success' => false,
                'message' => 'Organization has no ASAAS integration',
                'status' => 'no_integration'
            ];
        }

        try {
            $environment = $organization->asaas->asaas_environment ?? AsaasEnvironment::SANDBOX;

            // Check subscription status if exists
            if ($organization->asaas->asaas_account_id) {
                return $this->checkSubscriptionFromAsaas($organization, $environment);
            }

            // Check for any payments if no subscription
            return $this->checkPaymentsFromAsaas($organization, $environment);

        } catch (AsaasException $e) {
            Log::error('Failed to check subscription status from ASAAS', [
                'organization_id' => $organization->id,
                'error' => $e->getMessage(),
                'asaas_error_code' => $e->getAsaasErrorCode()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to check status: ' . $e->getMessage(),
                'status' => 'error',
                'error_code' => $e->getAsaasErrorCode()
            ];
        }
    }

    /**
     * Check subscription from ASAAS
     *
     * @param Organization $organization
     * @param AsaasEnvironment|null $environment
     * @return array
     */
    protected function checkSubscriptionFromAsaas(Organization $organization, ?AsaasEnvironment $environment): array
    {
        try {
            // Get subscription details from ASAAS organization
            $asaasOrg = $organization->asaas;
            if (!$asaasOrg || !$asaasOrg->asaas_account_id) {
                throw new AsaasException('Organization does not have ASAAS account ID');
            }

            // Get account details instead of subscription
            $accountResponse = $this->asaasService->get(
                "/v3/accounts/{$asaasOrg->asaas_account_id}",
                [],
                $asaasOrg->asaas_api_key,
                $environment
            );

            // For now, we'll just return the account response since we don't have subscription endpoint
            // TODO: Implement proper subscription checking when AsaasSubscription repository is available

            return [
                'success' => true,
                'message' => 'Account status retrieved successfully',
                'status' => 'active', // TODO: Determine from account response
                'asaas_data' => [
                    'account' => $accountResponse
                ]
            ];



        } catch (AsaasException $e) {
            if ($e->getHttpStatusCode() === 404) {
                return [
                    'success' => false,
                    'message' => 'Account not found in ASAAS',
                    'status' => 'not_found'
                ];
            }

            throw $e;
        }
    }

    /**
     * Check payments from ASAAS (when no subscription exists)
     *
     * @param Organization $organization
     * @param AsaasEnvironment|null $environment
     * @return array
     */
    protected function checkPaymentsFromAsaas(Organization $organization, ?AsaasEnvironment $environment): array
    {
        try {
            // Get recent payments for this customer
            $asaasOrg = $organization->asaas;
            if (!$asaasOrg) {
                throw new AsaasException('Organization does not have ASAAS integration');
            }

            $paymentsResponse = $this->asaasService->get(
                '/v3/payments',
                [
                    'customer' => $asaasOrg->asaas_account_id,
                    'limit' => 10
                ],
                $asaasOrg->asaas_api_key,
                $environment
            );

            $payments = $paymentsResponse['data'] ?? [];

            if (empty($payments)) {
                return [
                    'success' => true,
                    'message' => 'No payments found',
                    'status' => 'no_payments'
                ];
            }

            // Find the most recent paid payment
            $paidPayments = array_filter($payments, function($payment) {
                return $payment['status'] === 'RECEIVED';
            });

            if (!empty($paidPayments)) {
                $latestPaidPayment = $paidPayments[0];

                // TODO: Update organization through repository instead of direct model access
                // For now, just return the payment data

                return [
                    'success' => true,
                    'message' => 'Payment found',
                    'status' => 'payment_found',
                    'latest_payment' => $latestPaidPayment
                ];
            }

            return [
                'success' => true,
                'message' => 'No paid payments found',
                'status' => 'no_paid_payments',
                'pending_payments' => count($payments)
            ];

        } catch (AsaasException $e) {
            throw $e;
        }
    }

    /**
     * Determine status from subscription data
     *
     * @param array $subscription
     * @param array|null $latestPayment
     * @return array
     */
    protected function determineStatusFromSubscription(array $subscription, ?array $latestPayment): array
    {
        $updateData = [
            'subscription_value' => $subscription['value'],
        ];

        // Update next due date if available
        if (isset($subscription['nextDueDate'])) {
            $updateData['subscription_due_date'] = $subscription['nextDueDate'];
        }

        // Determine status based on ASAAS subscription status
        switch ($subscription['status']) {
            case 'ACTIVE':
                $updateData['subscription_status'] = 'active';
                break;

            case 'OVERDUE':
                $updateData['subscription_status'] = 'overdue';
                break;

            case 'EXPIRED':
            case 'INACTIVE':
                $updateData['subscription_status'] = 'inactive';
                break;

            default:
                $updateData['subscription_status'] = 'inactive';
        }

        // If there's a latest payment, check its status
        if ($latestPayment) {
            switch ($latestPayment['status']) {
                case 'RECEIVED':
                    if ($updateData['subscription_status'] === 'overdue') {
                        $updateData['subscription_status'] = 'active';
                    }
                    break;

                case 'OVERDUE':
                    $updateData['subscription_status'] = 'overdue';
                    break;
            }
        }

        return $updateData;
    }

    /**
     * Determine status from payment data
     *
     * @param array $payment
     * @return array
     */
    protected function determineStatusFromPayment(array $payment): array
    {
        $updateData = [
            'subscription_value' => $payment['value'],
        ];

        // For one-time payments, calculate expiration based on payment date
        if ($payment['status'] === 'RECEIVED' && $payment['paymentDate']) {
            $paymentDate = Carbon::parse($payment['paymentDate']);
            $expiresAt = $paymentDate->addMonth(); // 1 month access

            $updateData['subscription_status'] = 'active';
            $updateData['subscription_expires_at'] = $expiresAt->toDateString();

            // Check if already expired
            if (Carbon::now()->gt($expiresAt)) {
                $updateData['subscription_status'] = 'inactive';
            }
        } else {
            $updateData['subscription_status'] = 'inactive';
        }

        return $updateData;
    }

    /**
     * Bulk check subscription status for multiple organizations
     *
     * @param array $organizationIds
     * @return array
     */
    public function bulkCheck(array $organizationIds): array
    {
        $results = [];
        $errors = [];

        foreach ($organizationIds as $organizationId) {
            try {
                // TODO: Use OrganizationRepository instead of direct model access
                throw new \Exception('Method needs to be implemented with proper repository pattern');

            } catch (\Exception $e) {
                $errors[$organizationId] = [
                    'error' => $e->getMessage(),
                    'organization_id' => $organizationId
                ];

                Log::error('Failed to check subscription status in bulk', [
                    'organization_id' => $organizationId,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return [
            'results' => $results,
            'errors' => $errors,
            'total_processed' => count($results),
            'total_errors' => count($errors)
        ];
    }

    /**
     * Check all organizations with ASAAS integration
     *
     * @return array
     */
    public function checkAllOrganizations(): array
    {
        // TODO: Use OrganizationRepository instead of direct model access
        throw new \Exception('Method needs to be implemented with proper repository pattern');
    }

    /**
     * Get subscription status summary
     *
     * @param Organization $organization
     * @return array
     */
    public function getStatusSummary(Organization $organization): array
    {
        $summary = [
            'organization_id' => $organization->id,
            'has_asaas_integration' => $organization->hasAsaasIntegration(),
            'is_courtesy' => $organization->is_courtesy,
        ];

        // TODO: Add subscription-related fields when AsaasSubscription repository is available
        // These fields don't exist in Organization domain:
        // - subscription_due_date
        // - subscription_expires_at
        // - subscription_status
        // - asaas_subscription_id

        return $summary;
    }
}
