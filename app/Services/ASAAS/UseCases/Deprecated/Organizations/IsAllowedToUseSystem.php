<?php

namespace App\Services\ASAAS\UseCases\Deprecated\Organizations;

use App\Domains\Organization as OrganizationDomain;
use App\Services\ASAAS\Domains\AsaasOrganization;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

/**
 * Validates if organizations are allowed to use the system.
 * Checks subscription status, courtesy access, payment status, and business rules.
 */
class IsAllowedToUseSystem
{
    /**
     * Check if organization is allowed to use the system
     *
     * @param OrganizationDomain $organization
     * @return array
     */
    public function perform(OrganizationDomain $organization): array
    {
        // First check basic organization status
        if (!$organization->is_active) {
            return [
                'allowed' => false,
                'reason' => 'organization_inactive',
                'message' => 'Organization is inactive',
                'details' => [
                    'is_active' => false,
                ]
            ];
        }

        // is_suspended doesn't exist in Organization domain
        // Skip suspension check for now

        // Check ASAAS integration and subscription status
        return $this->checkAsaasAccess($organization);
    }

    /**
     * Check ASAAS access (including courtesy and subscription)
     *
     * @param OrganizationDomain $organization
     * @return array
     */
    protected function checkAsaasAccess(OrganizationDomain $organization): array
    {
        // Check if organization has ASAAS integration
        if (!$organization->hasAsaasIntegration()) {
            Log::warning('Organization has no ASAAS integration', [
                'organization_id' => $organization->id
            ]);

            return [
                'allowed' => false,
                'reason' => 'no_asaas_integration',
                'message' => 'Organization has no ASAAS integration',
                'details' => [
                    'has_asaas_account' => false,
                ]
            ];
        }

        $asaasOrganization = $organization->asaas;

        // Check courtesy access first (courtesy is in Organization domain)
        if ($organization->is_courtesy) {
            return $this->checkCourtesyAccess($organization);
        }

        // Check subscription access
        return $this->checkSubscriptionAccess($asaasOrganization);
    }

    /**
     * Check courtesy access
     *
     * @param OrganizationDomain $organization
     * @return array
     */
    protected function checkCourtesyAccess(OrganizationDomain $organization): array
    {
        $now = Carbon::now();

        // If no expiration date, courtesy is permanent
        if (!$organization->courtesy_expires_at) {
            Log::info('Organization has permanent courtesy access', [
                'organization_id' => $organization->id,
                'courtesy_reason' => $organization->courtesy_reason
            ]);

            return [
                'allowed' => true,
                'reason' => 'courtesy_permanent',
                'message' => 'Organization has permanent courtesy access',
                'details' => [
                    'is_courtesy' => true,
                    'courtesy_expires_at' => null,
                    'courtesy_reason' => $organization->courtesy_reason,
                ]
            ];
        }

        // Check if courtesy is still valid
        $courtesyExpiresAt = $organization->courtesy_expires_at;

        if ($now->lte($courtesyExpiresAt)) {
            $daysRemaining = $now->diffInDays($courtesyExpiresAt);

            Log::info('Organization has valid courtesy access', [
                'organization_id' => $organization->id,
                'expires_at' => $courtesyExpiresAt->toDateString(),
                'days_remaining' => $daysRemaining
            ]);

            return [
                'allowed' => true,
                'reason' => 'courtesy_active',
                'message' => "Courtesy access valid until {$courtesyExpiresAt->format('Y-m-d')}",
                'details' => [
                    'is_courtesy' => true,
                    'courtesy_expires_at' => $courtesyExpiresAt->toDateString(),
                    'days_remaining' => $daysRemaining,
                    'courtesy_reason' => $organization->courtesy_reason,
                ]
            ];
        }

        // Courtesy has expired
        Log::warning('Organization courtesy access has expired', [
            'organization_id' => $organization->id,
            'expired_at' => $courtesyExpiresAt->toDateString(),
            'days_expired' => $courtesyExpiresAt->diffInDays($now)
        ]);

        return [
            'allowed' => false,
            'reason' => 'courtesy_expired',
            'message' => "Courtesy access expired on {$courtesyExpiresAt->format('Y-m-d')}",
            'details' => [
                'is_courtesy' => true,
                'courtesy_expires_at' => $courtesyExpiresAt->toDateString(),
                'days_expired' => $courtesyExpiresAt->diffInDays($now),
                'courtesy_reason' => $organization->courtesy_reason,
            ]
        ];
    }

    /**
     * Check subscription access
     *
     * @param AsaasOrganization $asaasOrganization
     * @return array
     */
    protected function checkSubscriptionAccess(AsaasOrganization $asaasOrganization): array
    {
        // TODO: subscription_status doesn't exist in AsaasOrganization domain
        // For now, we'll use basic logic based on is_active

        if ($asaasOrganization->is_active) {
            return [
                'allowed' => true,
                'reason' => 'subscription_active',
                'message' => 'Organization has active ASAAS account',
                'details' => [
                    'has_asaas_account' => true,
                    'is_active' => true,
                ]
            ];
        }

        return [
            'allowed' => false,
            'reason' => 'subscription_inactive',
            'message' => 'ASAAS account is inactive',
            'details' => [
                'has_asaas_account' => true,
                'is_active' => false,
            ]
        ];
    }

    /**
     * Check active subscription
     * TODO: Remove this method as it's no longer used after refactoring checkSubscriptionAccess
     *
     * @param AsaasOrganization $asaasOrganization
     * @return array
     */
    protected function checkActiveSubscription(AsaasOrganization $asaasOrganization): array
    {
        // This method is no longer used
        return [
            'allowed' => true,
            'reason' => 'subscription_active',
            'message' => 'Active subscription',
            'details' => []
        ];
    }

    /**
     * Check overdue subscription
     * TODO: Remove this method as it's no longer used after refactoring checkSubscriptionAccess
     *
     * @param AsaasOrganization $asaasOrganization
     * @return array
     */
    protected function checkOverdueSubscription(AsaasOrganization $asaasOrganization): array
    {
        // This method is no longer used
        return [
            'allowed' => false,
            'reason' => 'subscription_overdue',
            'message' => 'Subscription is overdue',
            'details' => []
        ];
    }

    /**
     * Get access summary for organization
     *
     * @param OrganizationDomain $organization
     * @return array
     */
    public function getAccessSummary(OrganizationDomain $organization): array
    {
        $result = $this->perform($organization);

        return [
            'organization_id' => $organization->id,
            'organization_name' => $organization->name,
            'allowed' => $result['allowed'],
            'access_type' => $this->determineAccessType($organization),
            'status_summary' => $this->getStatusSummary($organization),
            'next_action_required' => $this->getNextActionRequired($organization, $result),
        ];
    }

    /**
     * Determine access type
     *
     * @param OrganizationDomain $organization
     * @return string
     */
    protected function determineAccessType(OrganizationDomain $organization): string
    {
        if (!$organization->hasAsaasIntegration()) {
            return 'none';
        }

        if ($organization->is_courtesy) {
            return 'courtesy';
        }

        return 'subscription';
    }

    /**
     * Get status summary
     *
     * @param OrganizationDomain $organization
     * @return array
     */
    protected function getStatusSummary(OrganizationDomain $organization): array
    {
        $asaasOrganization = $organization->asaas;

        return [
            'is_active' => $organization->is_active,
            // is_suspended doesn't exist in Organization domain
            'is_courtesy' => $organization->is_courtesy ?? false,
            'has_asaas_account' => $organization->hasAsaasIntegration(),
            // subscription_status doesn't exist in AsaasOrganization domain
        ];
    }

    /**
     * Get next action required
     *
     * @param OrganizationDomain $organization
     * @param array $accessResult
     * @return string|null
     */
    protected function getNextActionRequired(OrganizationDomain $organization, array $accessResult): ?string
    {
        if (!$accessResult['allowed']) {
            switch ($accessResult['reason']) {
                case 'organization_inactive':
                    return 'activate_organization';
                case 'organization_suspended':
                    return 'unsuspend_organization';
                case 'no_asaas_integration':
                    return 'create_asaas_subaccount';
                case 'subscription_inactive':
                    return 'create_subscription';
                case 'subscription_overdue_expired':
                case 'subscription_overdue':
                    return 'pay_overdue_subscription';
                case 'subscription_expired':
                    return 'renew_subscription';
                case 'courtesy_expired':
                    return 'create_subscription_or_extend_courtesy';
                default:
                    return 'contact_support';
            }
        }

        return null;
    }
}
