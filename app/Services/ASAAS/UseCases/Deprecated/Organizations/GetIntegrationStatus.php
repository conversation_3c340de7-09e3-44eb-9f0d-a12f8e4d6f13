<?php

namespace App\Services\ASAAS\UseCases\Deprecated\Organizations;

use App\Domains\Organization as OrganizationDomain;
use App\Services\ASAAS\Repositories\AsaasOrganizationRepository;

/**
 * Retrieves ASAAS integration status for organizations.
 * Provides detailed information about sync status, API keys, and integration health.
 */
class GetIntegrationStatus
{
    protected AsaasOrganizationRepository $asaasOrganizationRepository;

    public function __construct(AsaasOrganizationRepository $asaasOrganizationRepository)
    {
        $this->asaasOrganizationRepository = $asaasOrganizationRepository;
    }

    /**
     * Get ASAAS integration status for organization
     *
     * @param OrganizationDomain $organization
     * @return array
     */
    public function perform(OrganizationDomain $organization): array
    {
        $asaasOrganization = $this->asaasOrganizationRepository->findByOrganizationId($organization->id);

        $status = [
            'organization_id' => $organization->id,
            'organization_name' => $organization->name,
            'has_asaas_integration' => $asaasOrganization !== null,
            'is_active' => $organization->is_active,
            // is_suspended doesn't exist in Organization domain
            'asaas_data' => null,
            'can_access_system' => false,
            'access_type' => 'none'
        ];

        if ($asaasOrganization) {
            $status['asaas_data'] = [
                'asaas_account_id' => $asaasOrganization->asaas_account_id,
                'asaas_environment' => $asaasOrganization->asaas_environment?->value,
                'is_courtesy' => $asaasOrganization->is_courtesy,
                'courtesy_expires_at' => $asaasOrganization->courtesy_expires_at?->toDateString(),
                'courtesy_reason' => $asaasOrganization->courtesy_reason,
                'is_active' => $asaasOrganization->is_active,
                'last_sync_at' => $asaasOrganization->last_sync_at?->toDateTimeString(),
            ];

            // TODO: These methods need to be implemented in AsaasOrganization domain
            // For now, we'll use basic logic
            $status['can_access_system'] = $asaasOrganization->is_active;

            if ($asaasOrganization->is_courtesy) {
                $status['access_type'] = 'courtesy';
            } else {
                $status['access_type'] = 'subscription';
            }
        }

        // Check local courtesy (independent of ASAAS)
        $status['local_courtesy'] = [
            'is_courtesy' => $organization->is_courtesy ?? false,
            'courtesy_expires_at' => $organization->courtesy_expires_at,
            'courtesy_reason' => $organization->courtesy_reason
        ];

        // If has local courtesy, update access info
        if ($organization->is_courtesy) {
            $isLocalCourtesyActive = !$organization->courtesy_expires_at ||
                                   $organization->courtesy_expires_at->isFuture();

            if ($isLocalCourtesyActive) {
                $status['can_access_system'] = true;
                $status['access_type'] = 'local_courtesy';
            }
        }

        return $status;
    }
}
