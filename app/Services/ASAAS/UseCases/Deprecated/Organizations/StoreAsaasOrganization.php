<?php

namespace App\Services\ASAAS\UseCases\Deprecated\Organizations;

use App\Domains\Organization;
use App\Enums\AsaasEnvironment;
use App\Services\ASAAS\Domains\AsaasOrganization;
use App\Services\ASAAS\Factories\AsaasOrganizationFactory;
use App\Services\ASAAS\Repositories\AsaasOrganizationRepository;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * Stores ASAAS organization data locally.
 * Handles creation and persistence of organization integration records.
 */
class StoreAsaasOrganization
{
    private AsaasOrganizationRepository $asaasOrganizationRepository;
    private AsaasOrganizationFactory $asaasOrganizationFactory;

    public function __construct(
        AsaasOrganizationRepository $asaasOrganizationRepository,
        AsaasOrganizationFactory $asaasOrganizationFactory
    ) {
        $this->asaasOrganizationRepository = $asaasOrganizationRepository;
        $this->asaasOrganizationFactory = $asaasOrganizationFactory;
    }

    /**
     * Store a new AsaasOrganization
     *
     * @param array $data
     * @param Organization $organization
     * @return AsaasOrganization
     */
    public function perform(array $data, Organization $organization): AsaasOrganization
    {
        // Check if organization already has an ASAAS integration
        $existingAsaasOrganization = $this->asaasOrganizationRepository->findByOrganizationId($organization->id);
        if ($existingAsaasOrganization) {
            throw new \InvalidArgumentException("Organization already has an ASAAS integration");
        }

        DB::beginTransaction();

        try {
            // Build domain from data
            $data['organization_id'] = $organization->id;
            $asaasOrganization = $this->asaasOrganizationFactory->buildFromStoreArray($data, $organization);

            // Store in database
            $storedAsaasOrganization = $this->asaasOrganizationRepository->store($asaasOrganization);

            DB::commit();

            Log::info('AsaasOrganization created successfully', [
                'organization_id' => $organization->id,
                'asaas_organization_id' => $storedAsaasOrganization->id,
                'asaas_account_id' => $storedAsaasOrganization->asaas_account_id,
            ]);

            return $storedAsaasOrganization;

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to create AsaasOrganization', [
                'organization_id' => $organization->id,
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            throw $e;
        }
    }

    /**
     * Store AsaasOrganization from ASAAS API response
     *
     * @param array $asaasResponse
     * @param Organization $organization
     * @param AsaasEnvironment|null $environment
     * @return AsaasOrganization
     */
    public function performFromAsaasResponse(
        array $asaasResponse,
        Organization $organization,
        ?AsaasEnvironment $environment = null
    ): AsaasOrganization {
        // Check if organization already has an ASAAS integration
        $existingAsaasOrganization = $this->asaasOrganizationRepository->findByOrganizationId($organization->id);
        if ($existingAsaasOrganization) {
            throw new \InvalidArgumentException("Organization already has an ASAAS integration");
        }

        DB::beginTransaction();

        try {
            // Build domain from ASAAS response
            $asaasOrganization = $this->asaasOrganizationFactory->buildFromAsaasResponse(
                $asaasResponse,
                $organization,
                $environment
            );

            // Store in database
            $storedAsaasOrganization = $this->asaasOrganizationRepository->store($asaasOrganization);

            DB::commit();

            Log::info('AsaasOrganization created from ASAAS response', [
                'organization_id' => $organization->id,
                'asaas_organization_id' => $storedAsaasOrganization->id,
                'asaas_account_id' => $storedAsaasOrganization->asaas_account_id,
                'environment' => $environment?->value ?? 'sandbox'
            ]);

            return $storedAsaasOrganization;

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to create AsaasOrganization from ASAAS response', [
                'organization_id' => $organization->id,
                'error' => $e->getMessage(),
                'asaas_response' => $asaasResponse
            ]);

            throw $e;
        }
    }
}
