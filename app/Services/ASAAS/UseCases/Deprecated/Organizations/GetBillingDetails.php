<?php

namespace App\Services\ASAAS\UseCases\Deprecated\Organizations;

use App\Domains\Organization;
use App\Enums\AsaasEnvironment;
use App\Services\ASAAS\AsaasService;
use App\Services\ASAAS\Exceptions\AsaasException;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

/**
 * Retrieves comprehensive billing information for organizations.
 * Handles subscription details, payment history, and billing status from ASAAS.
 */
class GetBillingDetails
{
    protected AsaasService $asaasService;
    protected IsAllowedToUseSystem $isAllowedToUseSystem;

    public function __construct(
        AsaasService $asaasService,
        IsAllowedToUseSystem $isAllowedToUseSystem
    ) {
        $this->asaasService = $asaasService;
        $this->isAllowedToUseSystem = $isAllowedToUseSystem;
    }

    /**
     * Get complete billing details for organization
     *
     * @param Organization $organization
     * @return array
     */
    public function perform(Organization $organization): array
    {
        // Get access status
        $accessStatus = $this->isAllowedToUseSystem->perform($organization);

        // Build billing details
        $billingDetails = [
            'organization' => $this->getOrganizationSummary($organization),
            'access_status' => $accessStatus,
            'asaas_integration' => $this->getAsaasIntegrationDetails($organization),
            'subscription' => $this->getSubscriptionDetails($organization),
            'courtesy' => $this->getCourtesyDetails($organization),
            'billing_history' => [],
            'next_actions' => $this->getNextActions($organization, $accessStatus),
        ];

        // Get billing history if ASAAS integration exists
        if ($organization->hasAsaasIntegration()) {
            try {
                $billingDetails['billing_history'] = $this->getBillingHistory($organization);
            } catch (\Exception $e) {
                Log::warning('Failed to get billing history', [
                    'organization_id' => $organization->id,
                    'error' => $e->getMessage()
                ]);
                $billingDetails['billing_history'] = [];
            }
        }

        return $billingDetails;
    }

    /**
     * Get organization summary
     *
     * @param Organization $organization
     * @return array
     */
    protected function getOrganizationSummary(Organization $organization): array
    {
        return [
            'id' => $organization->id,
            'name' => $organization->name,
            'description' => $organization->description,
            'is_active' => $organization->is_active,
            'is_suspended' => $organization->is_suspended,
            'created_at' => $organization->created_at?->format('Y-m-d'),
            // Additional details would come from ASAAS organization if available
            'asaas_email' => $organization->asaas?->email,
            'asaas_document' => $organization->asaas?->cpf_cnpj,
        ];
    }

    /**
     * Get ASAAS integration details
     *
     * @param Organization $organization
     * @return array
     */
    protected function getAsaasIntegrationDetails(Organization $organization): array
    {
        $asaas = $organization->asaas;
        return [
            'has_integration' => $organization->hasAsaasIntegration(),
            'account_id' => $asaas?->asaas_account_id,
            'wallet_id' => $asaas?->asaas_wallet_id,
            'environment' => $asaas?->asaas_environment?->value,
            'integration_date' => $asaas?->created_at?->format('Y-m-d'),
        ];
    }

    /**
     * Get subscription details
     *
     * @param Organization $organization
     * @return array
     */
    protected function getSubscriptionDetails(Organization $organization): array
    {
        // Note: Subscription details would need to be retrieved from AsaasSubscription repository
        // For now, returning basic structure
        $details = [
            'has_subscription' => false, // Would need repository lookup
            'subscription_id' => null,
            'status' => null,
            'value' => null,
            'started_at' => null,
            'expires_at' => null,
            'due_date' => null,
        ];

        // Add calculated fields - subscription_due_date doesn't exist in Organization domain
        // This would need to come from AsaasSubscription or AsaasOrganization
        if ($organization->asaas) {
            // TODO: Get subscription details from AsaasSubscription repository
            // For now, we'll leave these fields null
        }

        return $details;
    }

    /**
     * Get courtesy details
     *
     * @param Organization $organization
     * @return array
     */
    protected function getCourtesyDetails(Organization $organization): array
    {
        $details = [
            'is_courtesy' => $organization->is_courtesy,
            'expires_at' => $organization->courtesy_expires_at,
            'reason' => $organization->courtesy_reason,
        ];

        // Add calculated fields for courtesy
        if ($organization->is_courtesy && $organization->courtesy_expires_at) {
            $expiresAt = Carbon::parse($organization->courtesy_expires_at);
            $now = Carbon::now();

            $details['days_until_expiry'] = $now->diffInDays($expiresAt, false);
            $details['is_expired'] = $now->gt($expiresAt);

            if ($details['is_expired']) {
                $details['days_expired'] = $expiresAt->diffInDays($now);
            }
        }

        return $details;
    }

    /**
     * Get billing history from ASAAS
     *
     * @param Organization $organization
     * @return array
     */
    protected function getBillingHistory(Organization $organization): array
    {
        try {
            $asaas = $organization->asaas;
            if (!$asaas) {
                return [];
            }

            $environment = $asaas->asaas_environment ?? AsaasEnvironment::SANDBOX;

            // Get payments for this customer
            $paymentsResponse = $this->asaasService->get(
                '/v3/payments',
                [
                    'customer' => $asaas->asaas_account_id,
                    'limit' => 50,
                    'offset' => 0
                ],
                $asaas->asaas_api_key,
                $environment
            );

            $payments = $paymentsResponse['data'] ?? [];

            // Get subscription payments if subscription exists
            // Note: This would need AsaasSubscription repository lookup
            $subscriptionPayments = [];

            // Merge and format payments
            $allPayments = array_merge($payments, $subscriptionPayments);

            return array_map(function ($payment) {
                return [
                    'id' => $payment['id'],
                    'value' => $payment['value'],
                    'status' => $payment['status'],
                    'billing_type' => $payment['billingType'],
                    'due_date' => $payment['dueDate'],
                    'payment_date' => $payment['paymentDate'] ?? null,
                    'description' => $payment['description'] ?? '',
                    'invoice_url' => $payment['invoiceUrl'] ?? null,
                    'bank_slip_url' => $payment['bankSlipUrl'] ?? null,
                    'pix_qr_code' => $payment['qrCode']['payload'] ?? null,
                ];
            }, $allPayments);

        } catch (AsaasException $e) {
            Log::error('Failed to get billing history from ASAAS', [
                'organization_id' => $organization->id,
                'error' => $e->getMessage(),
                'asaas_error_code' => $e->getAsaasErrorCode()
            ]);

            return [];
        }
    }

    /**
     * Get next actions required
     *
     * @param Organization $organization
     * @param array $accessStatus
     * @return array
     */
    protected function getNextActions(Organization $organization, array $accessStatus): array
    {
        $actions = [];

        if (!$accessStatus['allowed']) {
            switch ($accessStatus['reason']) {
                case 'no_asaas_integration':
                    $actions[] = [
                        'action' => 'create_asaas_subaccount',
                        'title' => 'Criar Integração ASAAS',
                        'description' => 'Criar subconta no ASAAS para habilitar cobrança',
                        'priority' => 'high',
                        'url' => '/organizations/' . $organization->id . '/asaas/create-subaccount'
                    ];
                    break;

                case 'subscription_inactive':
                    $actions[] = [
                        'action' => 'create_subscription',
                        'title' => 'Criar Assinatura',
                        'description' => 'Criar assinatura do sistema',
                        'priority' => 'high',
                        'url' => '/organizations/' . $organization->id . '/billing/create-subscription'
                    ];
                    break;

                case 'subscription_overdue':
                case 'subscription_overdue_expired':
                    $actions[] = [
                        'action' => 'pay_overdue',
                        'title' => 'Pagar Pendência',
                        'description' => 'Pagar assinatura em atraso',
                        'priority' => 'urgent',
                        'url' => '/organizations/' . $organization->id . '/billing/pay-overdue'
                    ];
                    break;

                case 'subscription_expired':
                    $actions[] = [
                        'action' => 'renew_subscription',
                        'title' => 'Renovar Assinatura',
                        'description' => 'Renovar assinatura expirada',
                        'priority' => 'high',
                        'url' => '/organizations/' . $organization->id . '/billing/renew'
                    ];
                    break;

                case 'courtesy_expired':
                    $actions[] = [
                        'action' => 'create_subscription_or_extend_courtesy',
                        'title' => 'Cortesia Expirada',
                        'description' => 'Criar assinatura ou estender cortesia',
                        'priority' => 'high',
                        'url' => '/organizations/' . $organization->id . '/billing'
                    ];
                    break;
            }
        } else {
            // Organization has access, but check for upcoming actions
            // TODO: Get subscription due date from AsaasSubscription repository
            // For now, we'll skip this check since subscription_due_date doesn't exist in Organization domain

            if ($organization->is_courtesy && $organization->courtesy_expires_at) {
                $expiresAt = Carbon::parse($organization->courtesy_expires_at);
                $daysUntilExpiry = Carbon::now()->diffInDays($expiresAt, false);

                if ($daysUntilExpiry <= 14 && $daysUntilExpiry > 0) {
                    $actions[] = [
                        'action' => 'courtesy_expiring',
                        'title' => 'Cortesia Expirando',
                        'description' => "Cortesia expira em {$daysUntilExpiry} dias",
                        'priority' => 'medium',
                        'url' => '/organizations/' . $organization->id . '/billing/create-subscription'
                    ];
                }
            }
        }

        return $actions;
    }

    /**
     * Get billing summary for dashboard
     *
     * @param Organization $organization
     * @return array
     */
    public function getBillingSummary(Organization $organization): array
    {
        $accessStatus = $this->isAllowedToUseSystem->perform($organization);

        return [
            'status' => $accessStatus['allowed'] ? 'active' : 'inactive',
            'access_type' => $organization->is_courtesy ? 'courtesy' : 'subscription',
            'subscription_value' => null, // TODO: Get from AsaasSubscription repository
            'next_due_date' => null, // TODO: Get from AsaasSubscription repository
            'days_until_due' => null, // TODO: Calculate from AsaasSubscription
            'has_pending_actions' => !$accessStatus['allowed'],
            'status_message' => $accessStatus['message'],
        ];
    }
}
