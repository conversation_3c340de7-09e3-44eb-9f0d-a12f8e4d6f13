<?php

namespace App\Services\ASAAS\UseCases\Deprecated\Organizations;

use App\Services\ASAAS\Domains\AsaasOrganization;
use App\Services\ASAAS\Repositories\AsaasOrganizationRepository;
use Illuminate\Support\Collection;

/**
 * Finds and retrieves ASAAS organizations by various criteria.
 * Provides search functionality and bulk operations for organization lookup.
 */
class FindAsaasOrganization
{
    private AsaasOrganizationRepository $asaasOrganizationRepository;

    public function __construct(AsaasOrganizationRepository $asaasOrganizationRepository)
    {
        $this->asaasOrganizationRepository = $asaasOrganizationRepository;
    }

    /**
     * Find AsaasOrganization by ID
     *
     * @param int $asaasOrganizationId
     * @return AsaasOrganization|null
     */
    public function findById(int $asaasOrganizationId): ?AsaasOrganization
    {
        return $this->asaasOrganizationRepository->findById($asaasOrganizationId);
    }

    /**
     * Find AsaasOrganization by organization ID
     *
     * @param int $organizationId
     * @return AsaasOrganization|null
     */
    public function findByOrganizationId(int $organizationId): ?AsaasOrganization
    {
        return $this->asaasOrganizationRepository->findByOrganizationId($organizationId);
    }

    /**
     * Find AsaasOrganization by ASAAS account ID
     *
     * @param string $asaasAccountId
     * @return AsaasOrganization|null
     */
    public function findByAsaasAccountId(string $asaasAccountId): ?AsaasOrganization
    {
        return $this->asaasOrganizationRepository->findByAsaasAccountId($asaasAccountId);
    }

    /**
     * Get all active AsaasOrganizations
     *
     * @return Collection
     */
    public function getAllActive(): Collection
    {
        return $this->asaasOrganizationRepository->getActive();
    }

    /**
     * Get AsaasOrganizations that need sync
     *
     * @return Collection
     */
    public function getNeedingSync(): Collection
    {
        return $this->asaasOrganizationRepository->getNeedingSync();
    }

    /**
     * Check if organization has ASAAS integration
     *
     * @param int $organizationId
     * @return bool
     */
    public function hasAsaasIntegration(int $organizationId): bool
    {
        $asaasOrganization = $this->findByOrganizationId($organizationId);
        return $asaasOrganization !== null && $asaasOrganization->hasAsaasIntegration();
    }

    /**
     * Get AsaasOrganization with validation
     *
     * @param int $asaasOrganizationId
     * @return AsaasOrganization
     * @throws \InvalidArgumentException
     */
    public function getById(int $asaasOrganizationId): AsaasOrganization
    {
        $asaasOrganization = $this->findById($asaasOrganizationId);

        if (!$asaasOrganization) {
            throw new \InvalidArgumentException("AsaasOrganization not found with ID: {$asaasOrganizationId}");
        }

        return $asaasOrganization;
    }

    /**
     * Get AsaasOrganization by organization ID with validation
     *
     * @param int $organizationId
     * @return AsaasOrganization
     * @throws \InvalidArgumentException
     */
    public function getByOrganizationId(int $organizationId): AsaasOrganization
    {
        $asaasOrganization = $this->findByOrganizationId($organizationId);

        if (!$asaasOrganization) {
            throw new \InvalidArgumentException("AsaasOrganization not found for organization ID: {$organizationId}");
        }

        return $asaasOrganization;
    }

    /**
     * Get AsaasOrganization by ASAAS account ID with validation
     *
     * @param string $asaasAccountId
     * @return AsaasOrganization
     * @throws \InvalidArgumentException
     */
    public function getByAsaasAccountId(string $asaasAccountId): AsaasOrganization
    {
        $asaasOrganization = $this->findByAsaasAccountId($asaasAccountId);

        if (!$asaasOrganization) {
            throw new \InvalidArgumentException("AsaasOrganization not found for ASAAS account ID: {$asaasAccountId}");
        }

        return $asaasOrganization;
    }
}
