<?php

namespace App\Services\ASAAS\UseCases\Deprecated\Organizations;

use App\Services\ASAAS\Repositories\AsaasOrganizationRepository;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * Deletes ASAAS organization integrations (soft delete).
 * Handles cleanup of organization data while preserving historical records.
 */
class DeleteAsaasOrganization
{
    private AsaasOrganizationRepository $asaasOrganizationRepository;

    public function __construct(AsaasOrganizationRepository $asaasOrganizationRepository)
    {
        $this->asaasOrganizationRepository = $asaasOrganizationRepository;
    }

    /**
     * Delete an AsaasOrganization (soft delete)
     *
     * @param int $asaasOrganizationId
     * @return bool
     */
    public function perform(int $asaasOrganizationId): bool
    {
        // Find existing AsaasOrganization
        $existingAsaasOrganization = $this->asaasOrganizationRepository->findById($asaasOrganizationId);
        if (!$existingAsaasOrganization) {
            throw new \InvalidArgumentException("AsaasOrganization not found with ID: {$asaasOrganizationId}");
        }

        DB::beginTransaction();

        try {
            // Perform soft delete
            $deleted = $this->asaasOrganizationRepository->delete($asaasOrganizationId);

            if (!$deleted) {
                throw new \RuntimeException("Failed to delete AsaasOrganization");
            }

            DB::commit();

            Log::info('AsaasOrganization deleted successfully', [
                'asaas_organization_id' => $asaasOrganizationId,
                'organization_id' => $existingAsaasOrganization->organization_id,
                'asaas_account_id' => $existingAsaasOrganization->asaas_account_id
            ]);

            return true;

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to delete AsaasOrganization', [
                'asaas_organization_id' => $asaasOrganizationId,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Delete AsaasOrganization by organization ID
     *
     * @param int $organizationId
     * @return bool
     */
    public function performByOrganizationId(int $organizationId): bool
    {
        // Find existing AsaasOrganization by organization ID
        $existingAsaasOrganization = $this->asaasOrganizationRepository->findByOrganizationId($organizationId);
        if (!$existingAsaasOrganization) {
            throw new \InvalidArgumentException("AsaasOrganization not found for organization ID: {$organizationId}");
        }

        return $this->perform($existingAsaasOrganization->id);
    }
}
