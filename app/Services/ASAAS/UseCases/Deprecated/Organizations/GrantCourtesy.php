<?php

namespace App\Services\ASAAS\UseCases\Deprecated\Organizations;

use App\Domains\Organization as OrganizationDomain;
use App\Repositories\OrganizationRepository;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use InvalidArgumentException;

/**
 * Grants courtesy access to organizations independent of ASAAS billing.
 * Handles temporary access permissions, expiration dates, and courtesy reason tracking.
 */
class GrantCourtesy
{
    protected OrganizationRepository $organizationRepository;

    public function __construct(OrganizationRepository $organizationRepository)
    {
        $this->organizationRepository = $organizationRepository;
    }

    /**
     * Grant courtesy to organization (independent of ASAAS)
     *
     * @param OrganizationDomain $organization
     * @param string|null $expiresAt
     * @param string $reason
     * @return array
     * @throws Exception
     */
    public function perform(OrganizationDomain $organization, ?string $expiresAt, string $reason): array
    {
        DB::beginTransaction();

        try {
            // Parse expiration date
            $courtesyExpiresAt = null;
            if ($expiresAt) {
                $courtesyExpiresAt = Carbon::parse($expiresAt);

                // Validate that expiration is in the future
                if ($courtesyExpiresAt->isPast()) {
                    throw new InvalidArgumentException('Courtesy expiration date must be in the future');
                }
            }

            // Update organization with courtesy
            $organization->is_courtesy = true;
            $organization->courtesy_expires_at = $courtesyExpiresAt;
            $organization->courtesy_reason = $reason;

            // Save to database
            $updatedOrganization = $this->organizationRepository->update($organization);

            DB::commit();

            Log::info('Local courtesy granted to organization', [
                'organization_id' => $organization->id,
                'organization_name' => $organization->name,
                'courtesy_expires_at' => $courtesyExpiresAt?->toDateString(),
                'courtesy_reason' => $reason,
                'granted_by' => auth()->user()?->id ?? 'system'
            ]);

            return [
                'success' => true,
                'message' => 'Courtesy granted successfully',
                'is_courtesy' => true,
                'courtesy_expires_at' => $courtesyExpiresAt?->toDateString(),
                'courtesy_reason' => $reason,
                'organization' => $updatedOrganization
            ];

        } catch (Exception $e) {
            DB::rollBack();

            Log::error('Failed to grant courtesy to organization', [
                'organization_id' => $organization->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * Revoke courtesy from organization
     *
     * @param OrganizationDomain $organization
     * @param string $reason
     * @return array
     * @throws Exception
     */
    public function revoke(OrganizationDomain $organization, string $reason): array
    {
        DB::beginTransaction();

        try {
            // Update organization to remove courtesy
            $organization->is_courtesy = false;
            $organization->courtesy_expires_at = null;
            $organization->courtesy_reason = "Revoked: " . $reason;

            // Save to database
            $updatedOrganization = $this->organizationRepository->update($organization);

            DB::commit();

            Log::info('Local courtesy revoked from organization', [
                'organization_id' => $organization->id,
                'organization_name' => $organization->name,
                'revoke_reason' => $reason,
                'revoked_by' => auth()->user()?->id ?? 'system'
            ]);

            return [
                'success' => true,
                'message' => 'Courtesy revoked successfully',
                'is_courtesy' => false,
                'courtesy_expires_at' => null,
                'courtesy_reason' => "Revoked: " . $reason,
                'organization' => $updatedOrganization
            ];

        } catch (Exception $e) {
            DB::rollBack();

            Log::error('Failed to revoke courtesy from organization', [
                'organization_id' => $organization->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * Extend existing courtesy
     *
     * @param OrganizationDomain $organization
     * @param string $newExpiresAt
     * @param string $reason
     * @return array
     * @throws Exception
     */
    public function extend(OrganizationDomain $organization, string $newExpiresAt, string $reason): array
    {
        if (!$organization->is_courtesy) {
            throw new InvalidArgumentException('Organization is not currently in courtesy period');
        }

        $newExpirationDate = Carbon::parse($newExpiresAt);

        if ($newExpirationDate->isPast()) {
            throw new InvalidArgumentException('New expiration date must be in the future');
        }

        // If current courtesy has expiration, new date must be after current
        if ($organization->courtesy_expires_at && $newExpirationDate->isBefore($organization->courtesy_expires_at)) {
            throw new InvalidArgumentException('New expiration date must be after current expiration date');
        }

        DB::beginTransaction();

        try {
            $oldExpiresAt = $organization->courtesy_expires_at?->toDateString();

            // Update expiration date
            $organization->courtesy_expires_at = $newExpirationDate;
            $organization->courtesy_reason = $organization->courtesy_reason . " | Extended: " . $reason;

            // Save to database
            $updatedOrganization = $this->organizationRepository->update($organization);

            DB::commit();

            Log::info('Local courtesy extended for organization', [
                'organization_id' => $organization->id,
                'organization_name' => $organization->name,
                'old_expires_at' => $oldExpiresAt,
                'new_expires_at' => $newExpirationDate->toDateString(),
                'extend_reason' => $reason,
                'extended_by' => auth()->user()?->id ?? 'system'
            ]);

            return [
                'success' => true,
                'message' => 'Courtesy extended successfully',
                'is_courtesy' => true,
                'courtesy_expires_at' => $newExpirationDate->toDateString(),
                'courtesy_reason' => $organization->courtesy_reason,
                'organization' => $updatedOrganization
            ];

        } catch (Exception $e) {
            DB::rollBack();

            Log::error('Failed to extend courtesy for organization', [
                'organization_id' => $organization->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }
}
