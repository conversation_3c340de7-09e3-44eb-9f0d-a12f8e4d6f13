<?php

namespace App\Services\ASAAS\UseCases\Deprecated\Organizations;

use App\Domains\Organization;
use App\Enums\AsaasEnvironment;
use App\Services\ASAAS\AsaasService;
use App\Services\ASAAS\Exceptions\AsaasException;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * Creates system subscriptions for organizations in ASAAS.
 * Handles subscription setup, billing configuration, and payment method assignment.
 */
class CreateSystemSubscription
{
    protected AsaasService $asaasService;

    public function __construct(AsaasService $asaasService)
    {
        $this->asaasService = $asaasService;
    }

    /**
     * Create a system subscription for the organization
     *
     * @param Organization $organization
     * @param float $value Monthly subscription value
     * @param string $billingType Payment method (BOLETO, CREDIT_CARD, PIX)
     * @param array $options Additional options
     * @return array
     * @throws AsaasException
     */
    public function perform(
        Organization $organization,
        float $value,
        string $billingType = 'BOLETO',
        array $options = []
    ): array {
        // Validate prerequisites
        $this->validatePrerequisites($organization);

        // Prepare subscription data
        $subscriptionData = $this->prepareSubscriptionData($organization, $value, $billingType, $options);

        DB::beginTransaction();

        try {
            // Create subscription in ASAAS
            $environment = $organization->asaas?->asaas_environment ?? AsaasEnvironment::SANDBOX;
            $response = $this->asaasService->post(
                '/v3/subscriptions',
                $subscriptionData,
                null, // Use default token (master account)
                $environment
            );

            // Update organization with subscription data
            $this->updateOrganizationWithSubscriptionData($organization, $response, $value);

            DB::commit();

            Log::info('System subscription created successfully', [
                'organization_id' => $organization->id,
                'asaas_subscription_id' => $response['id'],
                'value' => $value,
                'billing_type' => $billingType
            ]);

            return [
                'success' => true,
                'organization' => $organization, // fresh() method doesn't exist in domains
                'subscription' => $response,
                'message' => 'System subscription created successfully'
            ];

        } catch (AsaasException $e) {
            DB::rollBack();

            Log::error('Failed to create system subscription', [
                'organization_id' => $organization->id,
                'error' => $e->getMessage(),
                'asaas_error_code' => $e->getAsaasErrorCode(),
                'response_data' => $e->getResponseData()
            ]);

            throw $e;

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Unexpected error creating system subscription', [
                'organization_id' => $organization->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw new AsaasException(
                'Failed to create subscription: ' . $e->getMessage(),
                0,
                $e
            );
        }
    }

    /**
     * Validate prerequisites for subscription creation
     *
     * @param Organization $organization
     * @throws AsaasException
     */
    protected function validatePrerequisites(Organization $organization): void
    {
        if (!$organization->hasAsaasIntegration()) {
            throw new AsaasException('Organization must have an ASAAS subaccount before creating subscription');
        }

        // Check if already has subscription via AsaasSubscription domain
        // This would need to be checked via repository if needed

        if (!$organization->is_active) {
            throw new AsaasException('Organization must be active to create subscription');
        }
    }

    /**
     * Prepare subscription data for ASAAS API
     *
     * @param Organization $organization
     * @param float $value
     * @param string $billingType
     * @param array $options
     * @return array
     */
    protected function prepareSubscriptionData(
        Organization $organization,
        float $value,
        string $billingType,
        array $options
    ): array {
        // Get ASAAS account ID from AsaasOrganization
        $asaasOrg = $organization->asaas;
        if (!$asaasOrg) {
            throw new AsaasException('Organization does not have ASAAS integration');
        }

        $data = [
            'customer' => $asaasOrg->asaas_account_id,
            'billingType' => $billingType,
            'value' => $value,
            'nextDueDate' => $options['next_due_date'] ?? Carbon::now()->addMonth()->format('Y-m-d'),
            'cycle' => $options['cycle'] ?? 'MONTHLY',
            'description' => $options['description'] ?? 'Assinatura do Sistema - ' . $organization->name,
            'externalReference' => 'ORG-' . $organization->id . '-SUBSCRIPTION',
        ];

        // Add optional fields
        if (isset($options['discount'])) {
            $data['discount'] = $options['discount'];
        }

        if (isset($options['interest'])) {
            $data['interest'] = $options['interest'];
        }

        if (isset($options['fine'])) {
            $data['fine'] = $options['fine'];
        }

        // Add notification settings
        $data['enableReminder'] = $options['enable_reminder'] ?? true;

        if (isset($options['reminder_days'])) {
            $data['reminderDays'] = $options['reminder_days'];
        }

        // Add credit card token if provided
        if ($billingType === 'CREDIT_CARD' && isset($options['credit_card_token'])) {
            $data['creditCardToken'] = $options['credit_card_token'];
        }

        return $data;
    }

    /**
     * Update organization with subscription data
     *
     * @param Organization $organization
     * @param array $response
     * @param float $value
     */
    protected function updateOrganizationWithSubscriptionData(
        Organization $organization,
        array $response,
        float $value
    ): void {
        // TODO: Update organization through repository instead of direct domain update
        // These fields don't exist in Organization domain:
        // - asaas_subscription_id
        // - subscription_status
        // - subscription_value
        // - subscription_due_date
        // - subscription_started_at
        // - subscription_expires_at

        // For now, we'll just log the subscription creation
        Log::info('Subscription data that would be updated', [
            'organization_id' => $organization->id,
            'asaas_subscription_id' => $response['id'],
            'value' => $value
        ]);
    }

    /**
     * Create a one-time payment instead of subscription
     *
     * @param Organization $organization
     * @param float $value
     * @param string $billingType
     * @param array $options
     * @return array
     * @throws AsaasException
     */
    public function createOneTimePayment(
        Organization $organization,
        float $value,
        string $billingType = 'BOLETO',
        array $options = []
    ): array {
        // Validate prerequisites
        $this->validatePrerequisites($organization);

        // Get ASAAS account ID from AsaasOrganization
        $asaasOrg = $organization->asaas;
        if (!$asaasOrg) {
            throw new AsaasException('Organization does not have ASAAS integration');
        }

        // Prepare payment data
        $paymentData = [
            'customer' => $asaasOrg->asaas_account_id,
            'billingType' => $billingType,
            'value' => $value,
            'dueDate' => $options['due_date'] ?? Carbon::now()->addDays(7)->format('Y-m-d'),
            'description' => $options['description'] ?? 'Pagamento do Sistema - ' . $organization->name,
            'externalReference' => 'ORG-' . $organization->id . '-PAYMENT-' . time(),
        ];

        // Add credit card token if provided
        if ($billingType === 'CREDIT_CARD' && isset($options['credit_card_token'])) {
            $paymentData['creditCardToken'] = $options['credit_card_token'];
        }

        DB::beginTransaction();

        try {
            // Create payment in ASAAS
            $environment = $asaasOrg->asaas_environment ?? AsaasEnvironment::SANDBOX;
            $response = $this->asaasService->post(
                '/v3/payments',
                $paymentData,
                [],
                $asaasOrg->asaas_api_key,
                $environment
            );

            // TODO: Update organization through repository instead of direct domain update
            // These fields don't exist in Organization domain:
            // - subscription_status
            // - subscription_value
            // - subscription_due_date

            DB::commit();

            Log::info('One-time payment created successfully', [
                'organization_id' => $organization->id,
                'asaas_payment_id' => $response['id'],
                'value' => $value,
                'billing_type' => $billingType
            ]);

            return [
                'success' => true,
                'organization' => $organization, // fresh() method doesn't exist in domains
                'payment' => $response,
                'message' => 'One-time payment created successfully'
            ];

        } catch (AsaasException $e) {
            DB::rollBack();
            throw $e;
        } catch (\Exception $e) {
            DB::rollBack();
            throw new AsaasException('Failed to create payment: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * Get subscription plans available
     *
     * @return array
     */
    public function getAvailablePlans(): array
    {
        return [
            'basic' => [
                'name' => 'Plano Básico',
                'value' => 29.90,
                'description' => 'Acesso básico ao sistema',
                'features' => [
                    'Até 100 usuários',
                    'Suporte por email',
                    'Relatórios básicos'
                ]
            ],
            'professional' => [
                'name' => 'Plano Profissional',
                'value' => 59.90,
                'description' => 'Acesso completo ao sistema',
                'features' => [
                    'Usuários ilimitados',
                    'Suporte prioritário',
                    'Relatórios avançados',
                    'Integrações API'
                ]
            ],
            'enterprise' => [
                'name' => 'Plano Empresarial',
                'value' => 99.90,
                'description' => 'Solução empresarial completa',
                'features' => [
                    'Tudo do Profissional',
                    'Suporte 24/7',
                    'Customizações',
                    'Gerente de conta dedicado'
                ]
            ]
        ];
    }

    /**
     * Calculate subscription value based on organization size
     *
     * @param Organization $organization
     * @return float
     */
    public function calculateSubscriptionValue(Organization $organization): float
    {
        // TODO: Get user count through repository instead of direct relationship access
        // users() relationship doesn't exist in Organization domain

        // For now, return base price
        return 29.90;
    }
}
