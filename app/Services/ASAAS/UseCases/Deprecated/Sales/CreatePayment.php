<?php

namespace App\Services\ASAAS\UseCases\Deprecated\Sales;

use App\Domains\Inventory\Sale as SaleDomain;
use App\Enums\AsaasEnvironment;
use App\Enums\PaymentStatus;
use App\Services\ASAAS\AsaasService;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Services\ASAAS\Factories\AsaasSaleFactory;
use App\Services\ASAAS\Repositories\AsaasSaleRepository;
use App\Services\ASAAS\UseCases\Deprecated\Clients\CreateCustomer;
use App\Services\ASAAS\UseCases\Sales\Sale;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CreatePayment
{
    protected AsaasService $asaasService;
    protected CreateCustomer $createCustomer;
    protected AsaasSaleRepository $asaasSaleRepository;
    protected AsaasSaleFactory $asaasSaleFactory;

    public function __construct(
        AsaasService $asaasService,
        CreateCustomer $createCustomer,
        AsaasSaleRepository $asaasSaleRepository,
        AsaasSaleFactory $asaasSaleFactory
    ) {
        $this->asaasService = $asaasService;
        $this->createCustomer = $createCustomer;
        $this->asaasSaleRepository = $asaasSaleRepository;
        $this->asaasSaleFactory = $asaasSaleFactory;
    }

    /**
     * Create payment in ASAAS
     *
     * @param SaleDomain $sale
     * @param array $paymentOptions
     * @param AsaasEnvironment|null $environment
     * @return array
     */
    public function perform(SaleDomain $sale, array $paymentOptions = [], ?AsaasEnvironment $environment = null): array
    {
        if ($sale->getAsaasPaymentId()) {
            throw new AsaasException('Sale already has an ASAAS payment ID');
        }

        // Validate prerequisites
        $this->validateSaleData($sale);

        // Ensure client has ASAAS customer
        $this->ensureClientHasAsaasCustomer($sale->client_id, $environment);

        DB::beginTransaction();

        try {
            // Prepare payment data for ASAAS
            $paymentData = $this->preparePaymentData($sale, $paymentOptions);

            // Get organization for API key
            $organization = $this->getOrganizationForSale($sale);

            // Create payment in ASAAS
            $asaasResponse = $this->asaasService->post(
                '/v3/payments',
                $paymentData,
                $organization->asaas_api_key,
                $environment ?: $organization->asaas_environment
            );

            // Update sale with ASAAS payment data
            $this->updateSaleWithAsaasData($sale, $asaasResponse, $paymentOptions);

            DB::commit();

            Log::info('ASAAS payment created successfully', [
                'sale_id' => $sale->id,
                'asaas_payment_id' => $asaasResponse['id'],
                'client_id' => $sale->client_id,
                'organization_id' => $sale->organization_id,
                'value' => $sale->total_value
            ]);

            return [
                'success' => true,
                'message' => 'Payment created successfully in ASAAS',
                'sale_id' => $sale->id,
                'asaas_response' => $asaasResponse
            ];

        } catch (AsaasException $e) {
            DB::rollBack();

            // Log sync error
            Log::error('Failed to create ASAAS payment', [
                'sale_id' => $sale->id,
                'error' => $e->getMessage(),
                'asaas_error_code' => $e->getAsaasErrorCode(),
                'response_data' => $e->getResponseData(),
                'timestamp' => now()->toISOString()
            ]);

            throw $e;

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to create ASAAS payment', [
                'sale_id' => $sale->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw new AsaasException('Failed to create payment: ' . $e->getMessage());
        }
    }

    /**
     * Validate sale data for ASAAS payment creation
     *
     * @param SaleDomain $sale
     * @throws AsaasException
     */
    protected function validateSaleData(SaleDomain $sale): void
    {
        $errors = [];

        if (!$sale->client_id) {
            $errors[] = 'Sale must have a client';
        }

        if (!$sale->organization_id) {
            $errors[] = 'Sale must belong to an organization';
        }

        // Organization ASAAS integration check would need repository lookup
        // Simplified for now

        if ($sale->total_value <= 0) {
            $errors[] = 'Sale value must be greater than zero';
        }

        if (!empty($errors)) {
            throw new AsaasException('Validation failed: ' . implode(', ', $errors));
        }
    }

    /**
     * Ensure client has ASAAS customer
     *
     * @param int $clientId
     * @param AsaasEnvironment|null $environment
     */
    protected function ensureClientHasAsaasCustomer(int $clientId, ?AsaasEnvironment $environment = null): void
    {
        // This method would need proper repository implementation to:
        // 1. Get client by ID
        // 2. Check if client has ASAAS customer
        // 3. Create customer if needed
        // For now, simplified implementation
        throw new AsaasException('ensureClientHasAsaasCustomer method needs proper repository implementation');
    }

    /**
     * Prepare payment data for ASAAS API
     *
     * @param Sale $sale
     * @param array $options
     * @return array
     */
    protected function preparePaymentData(Sale $sale, array $options): array
    {
        $data = [
            'customer' => $sale->client->asaas_customer_id,
            'value' => $sale->value,
            'dueDate' => $this->getDueDate($options)->format('Y-m-d'),
        ];

        // Add billing type if specified
        if (isset($options['billing_type'])) {
            $data['billingType'] = $options['billing_type'];
        }

        // Add description
        $data['description'] = $options['description'] ?? $this->generateDescription($sale);

        // Add external reference for tracking
        $data['externalReference'] = "sale_{$sale->id}";

        // Add installment information if applicable
        if (isset($options['installment_count']) && $options['installment_count'] > 1) {
            $data['installmentCount'] = $options['installment_count'];
            $data['installmentValue'] = $options['installment_value'] ?? ($sale->value / $options['installment_count']);
            $data['totalValue'] = $sale->value;
        }

        // Add discount information
        if (isset($options['discount'])) {
            $discount = $options['discount'];
            if (isset($discount['value'])) {
                $data['discount'] = [
                    'value' => $discount['value'],
                    'dueDateLimitDays' => $discount['due_date_limit_days'] ?? 0,
                ];
            }
        }

        // Add interest and fine information
        if (isset($options['interest'])) {
            $data['interest'] = [
                'value' => $options['interest']['value'] ?? 0,
            ];
        }

        if (isset($options['fine'])) {
            $data['fine'] = [
                'value' => $options['fine']['value'] ?? 0,
            ];
        }

        // Add notification settings
        if (isset($options['notifications'])) {
            $data['enableReminder'] = $options['notifications']['enable_reminder'] ?? true;
            if (isset($options['notifications']['reminder_days'])) {
                $data['reminderDays'] = $options['notifications']['reminder_days'];
            }
        }

        // Add credit card information if applicable
        if (isset($options['credit_card'])) {
            $data['creditCard'] = $options['credit_card'];
            $data['creditCardHolderInfo'] = $options['credit_card_holder_info'] ?? [];
        }

        // Add PIX settings
        if (isset($options['pix'])) {
            $data['pix'] = $options['pix'];
        }

        return $data;
    }

    /**
     * Get due date for payment
     *
     * @param array $options
     * @return Carbon
     */
    protected function getDueDate(array $options): Carbon
    {
        if (isset($options['due_date'])) {
            return Carbon::parse($options['due_date']);
        }

        // Default to 7 days from now
        return Carbon::now()->addDays(7);
    }

    /**
     * Generate description for payment
     *
     * @param Sale $sale
     * @return string
     */
    protected function generateDescription(Sale $sale): string
    {
        return "Venda #{$sale->id} - {$sale->client->name}";
    }

    /**
     * Update sale with ASAAS payment data
     *
     * @param Sale $sale
     * @param array $asaasResponse
     * @param array $options
     */
    protected function updateSaleWithAsaasData(Sale $sale, array $asaasResponse, array $options): void
    {
        $updateData = [
            'asaas_payment_id' => $asaasResponse['id'],
            'payment_status' => PaymentStatus::fromAsaasStatus($asaasResponse['status']),
            'billing_type' => $asaasResponse['billingType'] ?? null,
            'due_date' => isset($asaasResponse['dueDate']) ? Carbon::parse($asaasResponse['dueDate']) : null,
            'net_value' => $asaasResponse['netValue'] ?? null,
            'original_value' => $asaasResponse['originalValue'] ?? $sale->value,
            'external_reference' => $asaasResponse['externalReference'] ?? null,
            'invoice_url' => $asaasResponse['invoiceUrl'] ?? null,
            'bank_slip_url' => $asaasResponse['bankSlipUrl'] ?? null,
            'asaas_synced_at' => now(),
            'asaas_sync_errors' => null,
        ];

        // Add installment information if applicable
        if (isset($asaasResponse['installmentCount'])) {
            $updateData['installment_count'] = $asaasResponse['installmentCount'];
            $updateData['installment_value'] = $asaasResponse['installmentValue'] ?? null;
        }

        // Add PIX QR code if available
        if (isset($asaasResponse['pix'])) {
            $updateData['pix_qr_code'] = $asaasResponse['pix']['qrCode'] ?? null;
        }

        // Add discount and interest values
        if (isset($asaasResponse['discount'])) {
            $updateData['discount_value'] = $asaasResponse['discount']['value'] ?? null;
        }

        if (isset($asaasResponse['interest'])) {
            $updateData['interest_value'] = $asaasResponse['interest']['value'] ?? null;
        }

        $sale->update($updateData);
    }

    /**
     * Create installment payment
     *
     * @param Sale $sale
     * @param int $installmentCount
     * @param array $options
     * @param AsaasEnvironment|null $environment
     * @return array
     */
    public function createInstallment(Sale $sale, int $installmentCount, array $options = [], ?AsaasEnvironment $environment = null): array
    {
        if ($installmentCount < 2) {
            throw new AsaasException('Installment count must be at least 2');
        }

        $installmentValue = $sale->value / $installmentCount;

        $paymentOptions = array_merge($options, [
            'installment_count' => $installmentCount,
            'installment_value' => $installmentValue,
        ]);

        return $this->perform($sale, $paymentOptions, $environment);
    }

    /**
     * Create payment with credit card
     *
     * @param Sale $sale
     * @param array $creditCardData
     * @param array $options
     * @param AsaasEnvironment|null $environment
     * @return array
     */
    public function createWithCreditCard(Sale $sale, array $creditCardData, array $options = [], ?AsaasEnvironment $environment = null): array
    {
        $paymentOptions = array_merge($options, [
            'billing_type' => 'CREDIT_CARD',
            'credit_card' => $creditCardData['card'],
            'credit_card_holder_info' => $creditCardData['holder_info'] ?? [],
        ]);

        return $this->perform($sale, $paymentOptions, $environment);
    }

    /**
     * Create PIX payment
     *
     * @param Sale $sale
     * @param array $options
     * @param AsaasEnvironment|null $environment
     * @return array
     */
    public function createPixPayment(Sale $sale, array $options = [], ?AsaasEnvironment $environment = null): array
    {
        $paymentOptions = array_merge($options, [
            'billing_type' => 'PIX',
        ]);

        return $this->perform($sale, $paymentOptions, $environment);
    }

    /**
     * Check if sale can create ASAAS payment
     *
     * @param Sale $sale
     * @return bool
     */
    public function canCreatePayment(Sale $sale): bool
    {
        // Already has ASAAS payment
        if ($sale->asaas_payment_id) {
            return false;
        }

        // Must have client
        if (!$sale->client_id) {
            return false;
        }

        // Organization must have ASAAS integration
        if (!$sale->organization_id) {
            return false;
        }

        // Must have positive value
        if ($sale->total_value <= 0) {
            return false;
        }

        return true;
    }

    /**
     * Get organization for sale
     *
     * @param SaleDomain $sale
     * @return \App\Services\ASAAS\Domains\AsaasOrganization
     * @throws AsaasException
     */
    protected function getOrganizationForSale(SaleDomain $sale): \App\Services\ASAAS\Domains\AsaasOrganization
    {
        // This would need to be implemented with proper repository injection
        // For now, throwing an exception to indicate missing implementation
        throw new AsaasException('getOrganizationForSale method needs proper repository implementation');
    }
}
