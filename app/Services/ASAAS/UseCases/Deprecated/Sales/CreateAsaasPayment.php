<?php

namespace App\Services\ASAAS\UseCases\Deprecated\Sales;

use App\Domains\Inventory\Sale as SaleDomain;
use App\Enums\AsaasEnvironment;
use App\Services\ASAAS\AsaasService;
use App\Services\ASAAS\Domains\AsaasSale;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Services\ASAAS\Factories\AsaasSaleFactory;
use App\Services\ASAAS\Repositories\AsaasClientRepository;
use App\Services\ASAAS\Repositories\AsaasOrganizationRepository;
use App\Services\ASAAS\Repositories\AsaasSaleRepository;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * Creates ASAAS payments from Sale entities.
 * Handles payment creation, customer validation, and local storage of payment data.
 */
class CreateAsaasPayment
{
    public function __construct(
        private AsaasService $asaasService,
        private AsaasSaleRepository $asaasSaleRepository,
        private AsaasClientRepository $asaasClientRepository,
        private AsaasOrganizationRepository $asaasOrganizationRepository,
        private AsaasSaleFactory $asaasSaleFactory
    ) {}

    /**
     * Create payment in ASAAS and AsaasSale entity
     *
     * @param SaleDomain $sale
     * @param string $billingType
     * @param AsaasEnvironment|null $environment
     * @return AsaasSale
     * @throws AsaasException
     */
    public function perform(SaleDomain $sale, string $billingType = 'BOLETO', ?AsaasEnvironment $environment = null): AsaasSale
    {
        // Check if sale already has AsaasSale
        $existingAsaasSale = $this->asaasSaleRepository->findBySaleId($sale->id);
        if ($existingAsaasSale) {
            throw new AsaasException('Sale already has an ASAAS payment integration');
        }

        // Check if client has AsaasClient
        $asaasClient = $this->asaasClientRepository->findByClientId($sale->client_id);
        if (!$asaasClient || !$asaasClient->hasAsaasIntegration()) {
            throw new AsaasException('Client must have ASAAS customer integration before creating payment');
        }

        // Validate required fields and get organization
        $organization = $this->validateSaleData($sale);

        DB::beginTransaction();

        try {
            // Create AsaasSale domain from Sale
            $asaasSaleDomain = $this->asaasSaleFactory->buildFromSale($sale, $asaasClient->asaas_customer_id);

            // Update billing type
            $asaasSaleDomain->billing_type = $billingType;

            // Prepare payment data for ASAAS API
            $paymentData = $asaasSaleDomain->toAsaasPayload();

            // Create payment in ASAAS
            $asaasResponse = $this->asaasService->post(
                '/v3/payments',
                $paymentData,
                $organization->asaas_api_key,
                $environment ?: AsaasEnvironment::SANDBOX
            );

            // Update AsaasSale with response data
            $asaasSaleDomain = $this->asaasSaleFactory->buildFromAsaasResponse(
                $asaasResponse,
                $sale->id,
                $sale->organization_id,
                $sale->client_id
            );

            // Save AsaasSale
            $savedAsaasSale = $this->asaasSaleRepository->create($asaasSaleDomain);

            DB::commit();

            Log::info('ASAAS payment created successfully', [
                'sale_id' => $sale->id,
                'asaas_payment_id' => $asaasResponse['id'],
                'organization_id' => $sale->organization_id,
                'billing_type' => $billingType,
                'value' => $sale->total_value
            ]);

            return $savedAsaasSale;

        } catch (AsaasException $e) {
            DB::rollBack();

            Log::error('Failed to create ASAAS payment', [
                'sale_id' => $sale->id,
                'error' => $e->getMessage(),
                'organization_id' => $sale->organization_id
            ]);

            throw $e;

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to create ASAAS payment', [
                'sale_id' => $sale->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw new AsaasException('Failed to create payment: ' . $e->getMessage());
        }
    }

    /**
     * Create payment by sale ID
     *
     * @param int $saleId
     * @param string $billingType
     * @param AsaasEnvironment|null $environment
     * @return AsaasSale
     * @throws AsaasException
     */
    public function performBySaleId(int $saleId, string $billingType = 'BOLETO', ?AsaasEnvironment $environment = null): AsaasSale
    {
        $sale = \App\Models\Sale::findOrFail($saleId);
        $saleDomain = app(\App\Factories\Inventory\SaleFactory::class)->buildFromModel($sale);

        return $this->perform($saleDomain, $billingType, $environment);
    }

    /**
     * Create payment with custom configuration
     *
     * @param SaleDomain $sale
     * @param array $paymentConfig
     * @param AsaasEnvironment|null $environment
     * @return AsaasSale
     * @throws AsaasException
     */
    public function performWithConfig(SaleDomain $sale, array $paymentConfig, ?AsaasEnvironment $environment = null): AsaasSale
    {
        // Check if sale already has AsaasSale
        $existingAsaasSale = $this->asaasSaleRepository->findBySaleId($sale->id);
        if ($existingAsaasSale) {
            throw new AsaasException('Sale already has an ASAAS payment integration');
        }

        // Check if client has AsaasClient
        $asaasClient = $this->asaasClientRepository->findByClientId($sale->client_id);
        if (!$asaasClient || !$asaasClient->hasAsaasIntegration()) {
            throw new AsaasException('Client must have ASAAS customer integration before creating payment');
        }

        // Validate required fields and get organization
        $organization = $this->validateSaleData($sale);

        DB::beginTransaction();

        try {
            // Create AsaasSale domain with custom config
            $asaasSaleDomain = $this->asaasSaleFactory->buildForCreation(
                $sale->id,
                $sale->organization_id,
                $sale->client_id,
                $asaasClient->asaas_customer_id,
                $paymentConfig['value'] ?? $sale->total_value,
                $paymentConfig['billing_type'] ?? 'BOLETO',
                \Carbon\Carbon::parse($paymentConfig['due_date'] ?? now()->addDays(7)),
                $paymentConfig['description'] ?? "Venda #{$sale->id}",
                $paymentConfig['external_reference'] ?? (string) $sale->id
            );

            // Apply additional config
            if (isset($paymentConfig['installment_count'])) {
                $asaasSaleDomain->installment_count = $paymentConfig['installment_count'];
                $asaasSaleDomain->installment_value = $asaasSaleDomain->value / $paymentConfig['installment_count'];
            }

            if (isset($paymentConfig['discount'])) {
                $asaasSaleDomain->discount_config = $paymentConfig['discount'];
            }

            if (isset($paymentConfig['fine'])) {
                $asaasSaleDomain->fine_config = $paymentConfig['fine'];
            }

            if (isset($paymentConfig['interest'])) {
                $asaasSaleDomain->interest_config = $paymentConfig['interest'];
            }

            // Prepare payment data for ASAAS API
            $paymentData = $asaasSaleDomain->toAsaasPayload();

            // Create payment in ASAAS
            $asaasResponse = $this->asaasService->post(
                '/v3/payments',
                $paymentData,
                $organization->asaas_api_key,
                $environment ?: AsaasEnvironment::SANDBOX
            );

            // Update AsaasSale with response data
            $asaasSaleDomain = $this->asaasSaleFactory->buildFromAsaasResponse(
                $asaasResponse,
                $sale->id,
                $sale->organization_id,
                $sale->client_id
            );

            // Save AsaasSale
            $savedAsaasSale = $this->asaasSaleRepository->create($asaasSaleDomain);

            DB::commit();

            Log::info('ASAAS payment created with custom config', [
                'sale_id' => $sale->id,
                'asaas_payment_id' => $asaasResponse['id'],
                'organization_id' => $sale->organization_id,
                'config' => $paymentConfig
            ]);

            return $savedAsaasSale;

        } catch (AsaasException $e) {
            DB::rollBack();

            Log::error('Failed to create ASAAS payment with config', [
                'sale_id' => $sale->id,
                'error' => $e->getMessage(),
                'config' => $paymentConfig
            ]);

            throw $e;

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to create ASAAS payment with config', [
                'sale_id' => $sale->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw new AsaasException('Failed to create payment: ' . $e->getMessage());
        }
    }

    /**
     * Validate sale data before creating payment
     *
     * @param SaleDomain $sale
     * @return \App\Services\ASAAS\Domains\AsaasOrganization
     * @throws AsaasException
     */
    protected function validateSaleData(SaleDomain $sale): \App\Services\ASAAS\Domains\AsaasOrganization
    {
        if (!$sale->id) {
            throw new AsaasException('Sale must be persisted before creating payment');
        }

        if (!$sale->client_id) {
            throw new AsaasException('Sale must have a client');
        }

        if (!$sale->total_value || $sale->total_value <= 0) {
            throw new AsaasException('Sale must have a valid total value');
        }

        // Get organization for API key
        $organization = $this->asaasOrganizationRepository->findByOrganizationId($sale->organization_id);
        if (!$organization || !$organization->hasAsaasIntegration()) {
            throw new AsaasException('Organization must have ASAAS integration');
        }

        return $organization;
    }

    /**
     * Get supported billing types
     *
     * @return array
     */
    public function getSupportedBillingTypes(): array
    {
        return ['BOLETO', 'CREDIT_CARD', 'PIX', 'UNDEFINED'];
    }

    /**
     * Validate billing type
     *
     * @param string $billingType
     * @throws AsaasException
     */
    public function validateBillingType(string $billingType): void
    {
        if (!in_array($billingType, $this->getSupportedBillingTypes())) {
            throw new AsaasException('Invalid billing type: ' . $billingType);
        }
    }
}
