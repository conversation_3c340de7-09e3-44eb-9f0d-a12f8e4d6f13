<?php

namespace App\Services\ASAAS\UseCases\Deprecated\Sales;

use App\Domains\Inventory\Sale as SaleDomain;
use App\Services\ASAAS\Repositories\AsaasSaleRepository;

class GetIntegrationStatus
{
    protected AsaasSaleRepository $asaasSaleRepository;

    public function __construct(AsaasSaleRepository $asaasSaleRepository)
    {
        $this->asaasSaleRepository = $asaasSaleRepository;
    }

    /**
     * Get ASAAS integration status for sale
     *
     * @param SaleDomain $sale
     * @return array
     */
    public function perform(SaleDomain $sale): array
    {
        $asaasSale = $this->asaasSaleRepository->findBySaleId($sale->id);

        $status = [
            'sale_id' => $sale->id,
            'organization_id' => $sale->organization_id,
            'client_id' => $sale->client_id,
            'total_value' => $sale->total_value,
            'has_asaas_integration' => $asaasSale !== null,
            'asaas_data' => null,
            'payment_status' => 'not_integrated'
        ];

        if ($asaasSale) {
            $status['asaas_data'] = [
                'asaas_payment_id' => $asaasSale->asaas_payment_id,
                'asaas_customer_id' => $asaasSale->asaas_customer_id,
                'payment_status' => $asaasSale->status,
                'payment_url' => $asaasSale->invoice_url,
                'due_date' => $asaasSale->due_date?->format('Y-m-d'),
                'billing_type' => $asaasSale->billing_type,
                'asaas_synced_at' => $asaasSale->asaas_synced_at?->format('Y-m-d H:i:s'),
                'asaas_sync_errors' => $asaasSale->asaas_sync_errors,
                'created_at' => $asaasSale->created_at?->format('Y-m-d H:i:s'),
                'updated_at' => $asaasSale->updated_at?->format('Y-m-d H:i:s'),
            ];

            // Determine payment status
            if ($asaasSale->asaas_sync_errors) {
                $status['payment_status'] = 'error';
            } elseif ($asaasSale->status) {
                $status['payment_status'] = $asaasSale->status;
            } elseif ($asaasSale->asaas_payment_id) {
                $status['payment_status'] = 'pending';
            } else {
                $status['payment_status'] = 'not_created';
            }
        }

        return $status;
    }
}
