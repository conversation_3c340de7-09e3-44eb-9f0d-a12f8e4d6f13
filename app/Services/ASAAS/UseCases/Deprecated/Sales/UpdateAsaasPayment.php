<?php

namespace App\Services\ASAAS\UseCases\Deprecated\Sales;

use App\Enums\AsaasEnvironment;
use App\Services\ASAAS\AsaasService;
use App\Services\ASAAS\Domains\AsaasSale;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Services\ASAAS\Factories\AsaasSaleFactory;
use App\Services\ASAAS\Repositories\AsaasOrganizationRepository;
use App\Services\ASAAS\Repositories\AsaasSaleRepository;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * Updates ASAAS payments both locally and in the ASAAS API.
 * Handles payment updates, validation, and synchronization.
 */
class UpdateAsaasPayment
{
    public function __construct(
        private AsaasService $asaasService,
        private AsaasSaleRepository $asaasSaleRepository,
        private AsaasOrganizationRepository $asaasOrganizationRepository,
        private AsaasSaleFactory $asaasSaleFactory
    ) {}

    /**
     * Update AsaasSale data in ASAAS API and local database
     *
     * @param AsaasSale $asaasSale
     * @param AsaasEnvironment|null $environment
     * @return AsaasSale
     * @throws AsaasException
     */
    public function perform(AsaasSale $asaasSale, ?AsaasEnvironment $environment = null): AsaasSale
    {
        if (!$asaasSale->hasAsaasIntegration()) {
            throw new AsaasException('AsaasSale does not have ASAAS payment ID');
        }

        // Get organization for API key
        $organization = $this->asaasOrganizationRepository->findByOrganizationId($asaasSale->organization_id);
        if (!$organization || !$organization->hasAsaasIntegration()) {
            throw new AsaasException('Organization must have ASAAS integration');
        }

        DB::beginTransaction();

        try {
            // Prepare payment data for ASAAS API
            $paymentData = $asaasSale->toAsaasPayload();

            // Update payment in ASAAS
            $asaasResponse = $this->asaasService->put(
                "/v3/payments/{$asaasSale->asaas_payment_id}",
                $paymentData,
                $organization->asaas_api_key,
                $environment ?: AsaasEnvironment::SANDBOX
            );

            // Update AsaasSale with response data
            $updatedAsaasSale = $this->asaasSaleFactory->buildFromAsaasResponse(
                $asaasResponse,
                $asaasSale->sale_id,
                $asaasSale->organization_id,
                $asaasSale->client_id
            );

            // Update in database
            $savedAsaasSale = $this->asaasSaleRepository->update($updatedAsaasSale);

            DB::commit();

            Log::info('ASAAS payment updated successfully', [
                'asaas_sale_id' => $asaasSale->id,
                'asaas_payment_id' => $asaasSale->asaas_payment_id,
                'organization_id' => $asaasSale->organization_id
            ]);

            return $savedAsaasSale;

        } catch (AsaasException $e) {
            DB::rollBack();

            // Mark sync error
            $errorData = [
                'error' => $e->getMessage(),
                'timestamp' => now()->toISOString(),
                'operation' => 'update',
            ];

            $this->asaasSaleRepository->markSyncError($asaasSale->id, $errorData);

            Log::error('Failed to update ASAAS payment', [
                'asaas_sale_id' => $asaasSale->id,
                'asaas_payment_id' => $asaasSale->asaas_payment_id,
                'error' => $e->getMessage()
            ]);

            throw $e;

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to update ASAAS payment', [
                'asaas_sale_id' => $asaasSale->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw new AsaasException('Failed to update payment: ' . $e->getMessage());
        }
    }

    /**
     * Update AsaasSale by sale ID
     *
     * @param int $saleId
     * @param AsaasEnvironment|null $environment
     * @return AsaasSale
     * @throws AsaasException
     */
    public function performBySaleId(int $saleId, ?AsaasEnvironment $environment = null): AsaasSale
    {
        $asaasSale = $this->asaasSaleRepository->findBySaleId($saleId);
        if (!$asaasSale) {
            throw new AsaasException('AsaasSale not found for sale ID: ' . $saleId);
        }

        return $this->perform($asaasSale, $environment);
    }

    /**
     * Update AsaasSale by ASAAS payment ID
     *
     * @param string $asaasPaymentId
     * @param AsaasEnvironment|null $environment
     * @return AsaasSale
     * @throws AsaasException
     */
    public function performByAsaasPaymentId(string $asaasPaymentId, ?AsaasEnvironment $environment = null): AsaasSale
    {
        $asaasSale = $this->asaasSaleRepository->findByAsaasPaymentId($asaasPaymentId);
        if (!$asaasSale) {
            throw new AsaasException('AsaasSale not found for ASAAS payment ID: ' . $asaasPaymentId);
        }

        return $this->perform($asaasSale, $environment);
    }

    /**
     * Update payment with custom data
     *
     * @param AsaasSale $asaasSale
     * @param array $updateData
     * @param AsaasEnvironment|null $environment
     * @return AsaasSale
     * @throws AsaasException
     */
    public function performWithData(AsaasSale $asaasSale, array $updateData, ?AsaasEnvironment $environment = null): AsaasSale
    {
        if (!$asaasSale->hasAsaasIntegration()) {
            throw new AsaasException('AsaasSale does not have ASAAS payment ID');
        }

        // Get organization for API key
        $organization = $this->asaasOrganizationRepository->findByOrganizationId($asaasSale->organization_id);
        if (!$organization || !$organization->hasAsaasIntegration()) {
            throw new AsaasException('Organization must have ASAAS integration');
        }

        DB::beginTransaction();

        try {
            // Apply updates to AsaasSale
            if (isset($updateData['due_date'])) {
                $asaasSale->due_date = \Carbon\Carbon::parse($updateData['due_date']);
            }

            if (isset($updateData['description'])) {
                $asaasSale->description = $updateData['description'];
            }

            if (isset($updateData['value'])) {
                $asaasSale->value = $updateData['value'];
            }

            if (isset($updateData['discount'])) {
                $asaasSale->discount_config = $updateData['discount'];
            }

            if (isset($updateData['fine'])) {
                $asaasSale->fine_config = $updateData['fine'];
            }

            if (isset($updateData['interest'])) {
                $asaasSale->interest_config = $updateData['interest'];
            }

            // Prepare payment data for ASAAS API
            $paymentData = $asaasSale->toAsaasPayload();

            // Update payment in ASAAS
            $asaasResponse = $this->asaasService->put(
                "/v3/payments/{$asaasSale->asaas_payment_id}",
                $paymentData,
                $organization->asaas_api_key,
                $environment ?: AsaasEnvironment::SANDBOX
            );

            // Update AsaasSale with response data
            $updatedAsaasSale = $this->asaasSaleFactory->buildFromAsaasResponse(
                $asaasResponse,
                $asaasSale->sale_id,
                $asaasSale->organization_id,
                $asaasSale->client_id
            );

            // Update in database
            $savedAsaasSale = $this->asaasSaleRepository->update($updatedAsaasSale);

            DB::commit();

            Log::info('ASAAS payment updated with custom data', [
                'asaas_sale_id' => $asaasSale->id,
                'asaas_payment_id' => $asaasSale->asaas_payment_id,
                'update_data' => $updateData
            ]);

            return $savedAsaasSale;

        } catch (AsaasException $e) {
            DB::rollBack();

            // Mark sync error
            $errorData = [
                'error' => $e->getMessage(),
                'timestamp' => now()->toISOString(),
                'operation' => 'update_with_data',
                'update_data' => $updateData,
            ];

            $this->asaasSaleRepository->markSyncError($asaasSale->id, $errorData);

            Log::error('Failed to update ASAAS payment with data', [
                'asaas_sale_id' => $asaasSale->id,
                'error' => $e->getMessage(),
                'update_data' => $updateData
            ]);

            throw $e;

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to update ASAAS payment with data', [
                'asaas_sale_id' => $asaasSale->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw new AsaasException('Failed to update payment: ' . $e->getMessage());
        }
    }

    /**
     * Bulk update multiple AsaasSales
     *
     * @param array $asaasSaleIds
     * @param AsaasEnvironment|null $environment
     * @return array
     */
    public function bulkUpdate(array $asaasSaleIds, ?AsaasEnvironment $environment = null): array
    {
        $results = [];
        $errors = [];

        foreach ($asaasSaleIds as $asaasSaleId) {
            try {
                $asaasSale = $this->asaasSaleRepository->findById($asaasSaleId);
                if (!$asaasSale) {
                    $errors[$asaasSaleId] = 'AsaasSale not found';
                    continue;
                }

                $result = $this->perform($asaasSale, $environment);
                $results[$asaasSaleId] = $result;

            } catch (\Exception $e) {
                $errors[$asaasSaleId] = $e->getMessage();

                Log::error('Failed to update ASAAS payment in bulk', [
                    'asaas_sale_id' => $asaasSaleId,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return [
            'results' => $results,
            'errors' => $errors,
            'total_processed' => count($results),
            'total_errors' => count($errors)
        ];
    }

    /**
     * Validate AsaasSale data before update
     *
     * @param AsaasSale $asaasSale
     * @throws AsaasException
     */
    protected function validateAsaasSaleData(AsaasSale $asaasSale): void
    {
        $errors = [];

        if (!$asaasSale->hasValidValue()) {
            $errors[] = 'Valid value is required';
        }

        if (!$asaasSale->due_date || $asaasSale->due_date->isPast()) {
            $errors[] = 'Valid future due date is required';
        }

        if ($asaasSale->isPaid()) {
            $errors[] = 'Cannot update paid payment';
        }

        if (!empty($errors)) {
            throw new AsaasException('Validation failed: ' . implode(', ', $errors));
        }
    }
}
