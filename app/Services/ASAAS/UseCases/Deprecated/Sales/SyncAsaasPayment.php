<?php

namespace App\Services\ASAAS\UseCases\Deprecated\Sales;

use App\Enums\AsaasEnvironment;
use App\Services\ASAAS\AsaasService;
use App\Services\ASAAS\Domains\AsaasSale;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Services\ASAAS\Factories\AsaasSaleFactory;
use App\Services\ASAAS\Repositories\AsaasOrganizationRepository;
use App\Services\ASAAS\Repositories\AsaasSaleRepository;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * Synchronizes ASAAS payment data with local AsaasSale entities.
 * Handles payment status updates, bulk synchronization, and error handling.
 */
class SyncAsaasPayment
{
    public function __construct(
        private AsaasService $asaasService,
        private AsaasSaleRepository $asaasSaleRepository,
        private AsaasOrganizationRepository $asaasOrganizationRepository,
        private AsaasSaleFactory $asaasSaleFactory
    ) {}

    /**
     * Sync AsaasSale data from ASAAS API
     *
     * @param AsaasSale $asaasSale
     * @param AsaasEnvironment|null $environment
     * @return AsaasSale
     * @throws AsaasException
     */
    public function perform(AsaasSale $asaasSale, ?AsaasEnvironment $environment = null): AsaasSale
    {
        if (!$asaasSale->hasAsaasIntegration()) {
            throw new AsaasException('AsaasSale does not have ASAAS payment ID');
        }

        // Get organization for API key
        $organization = $this->asaasOrganizationRepository->findByOrganizationId($asaasSale->organization_id);
        if (!$organization || !$organization->hasAsaasIntegration()) {
            throw new AsaasException('Organization must have ASAAS integration');
        }

        DB::beginTransaction();

        try {
            // Get payment data from ASAAS
            $asaasResponse = $this->asaasService->get(
                "/v3/payments/{$asaasSale->asaas_payment_id}",
                [], // query parameters
                $organization->asaas_api_key,
                $environment ?: AsaasEnvironment::SANDBOX
            );

            // Build updated AsaasSale from response
            $updatedAsaasSale = $this->asaasSaleFactory->buildFromAsaasResponse(
                $asaasResponse,
                $asaasSale->sale_id,
                $asaasSale->organization_id,
                $asaasSale->client_id
            );

            // Update in database
            $savedAsaasSale = $this->asaasSaleRepository->update($updatedAsaasSale);

            // Mark as synced
            $this->asaasSaleRepository->markAsSynced($savedAsaasSale->id, $asaasResponse);

            DB::commit();

            Log::info('ASAAS payment synced successfully', [
                'asaas_sale_id' => $asaasSale->id,
                'asaas_payment_id' => $asaasSale->asaas_payment_id,
                'organization_id' => $asaasSale->organization_id,
                'status' => $asaasResponse['status'] ?? 'unknown'
            ]);

            return $savedAsaasSale;

        } catch (AsaasException $e) {
            DB::rollBack();

            // Mark sync error
            $errorData = [
                'error' => $e->getMessage(),
                'timestamp' => now()->toISOString(),
                'operation' => 'sync',
            ];

            $this->asaasSaleRepository->markSyncError($asaasSale->id, $errorData);

            Log::error('Failed to sync ASAAS payment', [
                'asaas_sale_id' => $asaasSale->id,
                'asaas_payment_id' => $asaasSale->asaas_payment_id,
                'error' => $e->getMessage()
            ]);

            throw $e;

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to sync ASAAS payment', [
                'asaas_sale_id' => $asaasSale->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw new AsaasException('Failed to sync payment: ' . $e->getMessage());
        }
    }

    /**
     * Sync AsaasSale by sale ID
     *
     * @param int $saleId
     * @param AsaasEnvironment|null $environment
     * @return AsaasSale
     * @throws AsaasException
     */
    public function performBySaleId(int $saleId, ?AsaasEnvironment $environment = null): AsaasSale
    {
        $asaasSale = $this->asaasSaleRepository->findBySaleId($saleId);
        if (!$asaasSale) {
            throw new AsaasException('AsaasSale not found for sale ID: ' . $saleId);
        }

        return $this->perform($asaasSale, $environment);
    }

    /**
     * Sync AsaasSale by ASAAS payment ID
     *
     * @param string $asaasPaymentId
     * @param AsaasEnvironment|null $environment
     * @return AsaasSale
     * @throws AsaasException
     */
    public function performByAsaasPaymentId(string $asaasPaymentId, ?AsaasEnvironment $environment = null): AsaasSale
    {
        $asaasSale = $this->asaasSaleRepository->findByAsaasPaymentId($asaasPaymentId);
        if (!$asaasSale) {
            throw new AsaasException('AsaasSale not found for ASAAS payment ID: ' . $asaasPaymentId);
        }

        return $this->perform($asaasSale, $environment);
    }

    /**
     * Sync all AsaasSales that need sync
     *
     * @param AsaasEnvironment|null $environment
     * @return array
     */
    public function syncAllNeedingSync(?AsaasEnvironment $environment = null): array
    {
        $asaasSales = $this->asaasSaleRepository->getNeedingSync();

        Log::info('Starting bulk sync for AsaasSales', [
            'total_payments' => $asaasSales->count()
        ]);

        return $this->bulkSync($asaasSales->toArray(), $environment);
    }

    /**
     * Sync AsaasSales by organization
     *
     * @param int $organizationId
     * @param AsaasEnvironment|null $environment
     * @return array
     */
    public function syncByOrganization(int $organizationId, ?AsaasEnvironment $environment = null): array
    {
        $asaasSales = $this->asaasSaleRepository->getByOrganizationId($organizationId);

        Log::info('Starting sync for organization AsaasSales', [
            'organization_id' => $organizationId,
            'total_payments' => $asaasSales->count()
        ]);

        return $this->bulkSync($asaasSales->toArray(), $environment);
    }

    /**
     * Sync AsaasSales by status
     *
     * @param string $status
     * @param AsaasEnvironment|null $environment
     * @return array
     */
    public function syncByStatus(string $status, ?AsaasEnvironment $environment = null): array
    {
        $asaasSales = $this->asaasSaleRepository->getByStatus($status);

        Log::info('Starting sync for status AsaasSales', [
            'status' => $status,
            'total_payments' => $asaasSales->count()
        ]);

        return $this->bulkSync($asaasSales->toArray(), $environment);
    }

    /**
     * Bulk sync multiple AsaasSales
     *
     * @param array $asaasSales
     * @param AsaasEnvironment|null $environment
     * @return array
     */
    public function bulkSync(array $asaasSales, ?AsaasEnvironment $environment = null): array
    {
        $results = [];
        $errors = [];

        foreach ($asaasSales as $asaasSale) {
            try {
                if (!$asaasSale instanceof AsaasSale) {
                    $errors[$asaasSale['id'] ?? 'unknown'] = 'Invalid AsaasSale object';
                    continue;
                }

                $result = $this->perform($asaasSale, $environment);
                $results[$asaasSale->id] = $result;

            } catch (\Exception $e) {
                $errors[$asaasSale->id ?? 'unknown'] = $e->getMessage();

                Log::error('Failed to sync ASAAS payment in bulk', [
                    'asaas_sale_id' => $asaasSale->id ?? 'unknown',
                    'error' => $e->getMessage()
                ]);
            }
        }

        Log::info('Bulk sync completed', [
            'total_processed' => count($results),
            'total_errors' => count($errors)
        ]);

        return [
            'results' => $results,
            'errors' => $errors,
            'total_processed' => count($results),
            'total_errors' => count($errors)
        ];
    }

    /**
     * Force sync AsaasSale (ignore last sync time)
     *
     * @param AsaasSale $asaasSale
     * @param AsaasEnvironment|null $environment
     * @return AsaasSale
     * @throws AsaasException
     */
    public function forceSync(AsaasSale $asaasSale, ?AsaasEnvironment $environment = null): AsaasSale
    {
        Log::info('Force syncing ASAAS payment', [
            'asaas_sale_id' => $asaasSale->id,
            'asaas_payment_id' => $asaasSale->asaas_payment_id
        ]);

        return $this->perform($asaasSale, $environment);
    }

    /**
     * Check if AsaasSale needs sync
     *
     * @param AsaasSale $asaasSale
     * @return bool
     */
    public function needsSync(AsaasSale $asaasSale): bool
    {
        return $asaasSale->needsSync();
    }

    /**
     * Get sync status for AsaasSale
     *
     * @param AsaasSale $asaasSale
     * @return array
     */
    public function getSyncStatus(AsaasSale $asaasSale): array
    {
        return [
            'needs_sync' => $asaasSale->needsSync(),
            'has_errors' => $asaasSale->hasErrors(),
            'sync_status' => $asaasSale->sync_status,
            'last_synced_at' => $asaasSale->asaas_synced_at?->format('Y-m-d H:i:s'),
            'sync_errors' => $asaasSale->asaas_sync_errors,
            'payment_status' => $asaasSale->status,
            'is_paid' => $asaasSale->isPaid(),
            'is_overdue' => $asaasSale->isOverdue(),
        ];
    }

    /**
     * Sync payment status only (lightweight sync)
     *
     * @param AsaasSale $asaasSale
     * @param AsaasEnvironment|null $environment
     * @return AsaasSale
     * @throws AsaasException
     */
    public function syncStatusOnly(AsaasSale $asaasSale, ?AsaasEnvironment $environment = null): AsaasSale
    {
        if (!$asaasSale->hasAsaasIntegration()) {
            throw new AsaasException('AsaasSale does not have ASAAS payment ID');
        }

        // Get organization for API key
        $organization = $this->asaasOrganizationRepository->findByOrganizationId($asaasSale->organization_id);
        if (!$organization || !$organization->hasAsaasIntegration()) {
            throw new AsaasException('Organization must have ASAAS integration');
        }

        try {
            // Get payment data from ASAAS
            $asaasResponse = $this->asaasService->get(
                "/v3/payments/{$asaasSale->asaas_payment_id}",
                $organization->asaas_api_key,
                $environment ?: AsaasEnvironment::SANDBOX
            );

            // Update only status and payment date
            $this->asaasSaleRepository->updateStatus($asaasSale->id, $asaasResponse['status']);

            if (isset($asaasResponse['paymentDate'])) {
                $this->asaasSaleRepository->updatePaymentDate(
                    $asaasSale->id,
                    \Carbon\Carbon::parse($asaasResponse['paymentDate'])
                );
            }

            // Mark as synced
            $this->asaasSaleRepository->markAsSynced($asaasSale->id, $asaasResponse);

            Log::info('ASAAS payment status synced', [
                'asaas_sale_id' => $asaasSale->id,
                'asaas_payment_id' => $asaasSale->asaas_payment_id,
                'old_status' => $asaasSale->status,
                'new_status' => $asaasResponse['status']
            ]);

            // Return updated AsaasSale
            return $this->asaasSaleRepository->findById($asaasSale->id);

        } catch (AsaasException $e) {
            // Mark sync error
            $errorData = [
                'error' => $e->getMessage(),
                'timestamp' => now()->toISOString(),
                'operation' => 'sync_status_only',
            ];

            $this->asaasSaleRepository->markSyncError($asaasSale->id, $errorData);

            Log::error('Failed to sync ASAAS payment status', [
                'asaas_sale_id' => $asaasSale->id,
                'asaas_payment_id' => $asaasSale->asaas_payment_id,
                'error' => $e->getMessage()
            ]);

            throw $e;

        } catch (\Exception $e) {
            Log::error('Failed to sync ASAAS payment status', [
                'asaas_sale_id' => $asaasSale->id,
                'error' => $e->getMessage()
            ]);

            throw new AsaasException('Failed to sync payment status: ' . $e->getMessage());
        }
    }
}
