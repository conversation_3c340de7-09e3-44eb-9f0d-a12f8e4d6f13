<?php

namespace App\Services\ASAAS\UseCases\Deprecated\Sales;

use App\Domains\Inventory\Sale as SaleDomain;
use App\Enums\AsaasEnvironment;
use App\Enums\PaymentStatus;
use App\Services\ASAAS\AsaasService;
use App\Services\ASAAS\Domains\AsaasSale;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Services\ASAAS\Repositories\AsaasSaleRepository;
use App\Services\ASAAS\UseCases\Sales\Sale;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class SyncPaymentStatus
{
    protected AsaasService $asaasService;
    protected AsaasSaleRepository $asaasSaleRepository;

    public function __construct(
        AsaasService $asaasService,
        AsaasSaleRepository $asaasSaleRepository
    ) {
        $this->asaasService = $asaasService;
        $this->asaasSaleRepository = $asaasSaleRepository;
    }

    /**
     * Sync payment status from ASAAS
     *
     * @param SaleDomain $sale
     * @param AsaasEnvironment|null $environment
     * @return array
     */
    public function perform(SaleDomain $sale, ?AsaasEnvironment $environment = null): array
    {
        if (!$sale->hasAsaasPayment()) {
            return [
                'success' => false,
                'message' => 'Sale does not have ASAAS payment ID',
                'status' => 'no_payment_id'
            ];
        }

        // Check organization ASAAS integration (would need repository lookup)
        if (!$sale->organization_id) {
            return [
                'success' => false,
                'message' => 'Sale must belong to an organization',
                'status' => 'no_organization'
            ];
        }

        $asaasSale = $this->asaasSaleRepository->findBySaleId($sale->id);
        if (!$asaasSale) {
            return [
                'success' => false,
                'message' => 'Sale does not have ASAAS payment record',
                'status' => 'no_asaas_sale'
            ];
        }

        try {
            // Get payment data from ASAAS
            // Note: This would need proper organization repository lookup for API key
            $asaasResponse = $this->asaasService->get(
                "/v3/payments/{$asaasSale->asaas_payment_id}",
                null, // API key would need to be retrieved from organization
                $environment ?: AsaasEnvironment::SANDBOX
            );

            // Update ASAAS sale with latest data
            $this->updateAsaasSaleFromAsaasData($asaasSale, $asaasResponse);

            Log::info('Payment status synced successfully', [
                'sale_id' => $sale->id,
                'asaas_payment_id' => $asaasSale->asaas_payment_id,
                'new_status' => $asaasSale->status,
                'organization_id' => $sale->organization_id
            ]);

            return [
                'success' => true,
                'message' => 'Payment status synced successfully',
                'status' => 'synced',
                'sale_id' => $sale->id,
                'asaas_sale_id' => $asaasSale->id,
                'asaas_response' => $asaasResponse
            ];

        } catch (AsaasException $e) {
            // Store sync error in ASAAS sale
            $asaasSale->markSyncError([
                'error' => $e->getMessage(),
                'asaas_error_code' => $e->getAsaasErrorCode(),
                'response_data' => $e->getResponseData(),
            ]);

            Log::error('Failed to sync payment status from ASAAS', [
                'sale_id' => $sale->id,
                'asaas_payment_id' => $asaasSale->asaas_payment_id,
                'error' => $e->getMessage(),
                'asaas_error_code' => $e->getAsaasErrorCode()
            ]);

            return [
                'success' => false,
                'message' => 'ASAAS API error: ' . $e->getMessage(),
                'status' => 'asaas_error',
                'error_code' => $e->getAsaasErrorCode()
            ];

        } catch (\Exception $e) {
            Log::error('Failed to sync payment status', [
                'sale_id' => $sale->id,
                'asaas_payment_id' => $asaasSale->asaas_payment_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Sync error: ' . $e->getMessage(),
                'status' => 'sync_error'
            ];
        }
    }

    /**
     * Update ASAAS sale with ASAAS payment data
     *
     * @param AsaasSale $asaasSale
     * @param array $asaasData
     */
    protected function updateAsaasSaleFromAsaasData(AsaasSale $asaasSale, array $asaasData): void
    {
        $updateData = [
            'payment_status' => PaymentStatus::fromAsaasStatus($asaasData['status']),
            'billing_type' => $asaasData['billingType'] ?? $asaasSale->billing_type,
            'due_date' => isset($asaasData['dueDate']) ? Carbon::parse($asaasData['dueDate']) : $asaasSale->due_date,
            'net_value' => $asaasData['netValue'] ?? $asaasSale->net_value,
            'original_value' => $asaasData['originalValue'] ?? $asaasSale->original_value,
            'invoice_url' => $asaasData['invoiceUrl'] ?? $asaasSale->invoice_url,
            'bank_slip_url' => $asaasData['bankSlipUrl'] ?? $asaasSale->bank_slip_url,
            'asaas_synced_at' => now(),
            'asaas_sync_errors' => null,
        ];

        // Update payment date if payment was received
        if (isset($asaasData['paymentDate'])) {
            $updateData['payment_date'] = Carbon::parse($asaasData['paymentDate']);
        }

        // Update discount and interest values
        if (isset($asaasData['discount']['value'])) {
            $updateData['discount_value'] = $asaasData['discount']['value'];
        }

        if (isset($asaasData['interest']['value'])) {
            $updateData['interest_value'] = $asaasData['interest']['value'];
        }

        // Update PIX QR code if available
        if (isset($asaasData['pix']['qrCode'])) {
            $updateData['pix_qr_code'] = $asaasData['pix']['qrCode'];
        }

        // Update installment information
        if (isset($asaasData['installmentCount'])) {
            $updateData['installment_count'] = $asaasData['installmentCount'];
            $updateData['installment_value'] = $asaasData['installmentValue'] ?? null;
        }

        $asaasSale->update($updateData);
    }

    /**
     * Sync multiple payments
     *
     * @param array $saleIds
     * @param AsaasEnvironment|null $environment
     * @return array
     */
    public function syncMultiple(array $saleIds, ?AsaasEnvironment $environment = null): array
    {
        $results = [];
        $errors = [];

        foreach ($saleIds as $saleId) {
            try {
                $sale = Sale::with('organization')->findOrFail($saleId);
                $result = $this->perform($sale, $environment);

                if ($result['success']) {
                    $results[$saleId] = $result;
                } else {
                    $errors[$saleId] = $result;
                }

            } catch (\Exception $e) {
                $errors[$saleId] = [
                    'success' => false,
                    'message' => $e->getMessage(),
                    'sale_id' => $saleId
                ];

                Log::error('Failed to sync payment in bulk', [
                    'sale_id' => $saleId,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return [
            'results' => $results,
            'errors' => $errors,
            'total_synced' => count($results),
            'total_errors' => count($errors)
        ];
    }

    /**
     * Sync payments for organization
     *
     * @param int $organizationId
     * @param AsaasEnvironment|null $environment
     * @param int $limit
     * @return array
     */
    public function syncForOrganization(int $organizationId, ?AsaasEnvironment $environment = null, int $limit = 100): array
    {
        $sales = Sale::where('organization_id', $organizationId)
            ->whereHas('asaas')
            ->whereHas('asaas', function ($query) {
                $query->where(function ($q) {
                    $q->whereNull('asaas_synced_at')
                      ->orWhere('asaas_synced_at', '<', now()->subHours(1));
                });
            })
            ->limit($limit)
            ->get();

        $saleIds = $sales->pluck('id')->toArray();

        Log::info('Starting bulk payment sync for organization', [
            'organization_id' => $organizationId,
            'total_sales' => count($saleIds)
        ]);

        return $this->syncMultiple($saleIds, $environment);
    }

    /**
     * Sync overdue payments
     *
     * @param AsaasEnvironment|null $environment
     * @param int $limit
     * @return array
     */
    public function syncOverduePayments(?AsaasEnvironment $environment = null, int $limit = 100): array
    {
        $sales = Sale::whereHas('asaas', function ($query) {
                $query->where('payment_status', PaymentStatus::PENDING)
                      ->where('due_date', '<', now());
            })
            ->limit($limit)
            ->get();

        $saleIds = $sales->pluck('id')->toArray();

        Log::info('Starting overdue payments sync', [
            'total_sales' => count($saleIds)
        ]);

        return $this->syncMultiple($saleIds, $environment);
    }

    /**
     * Process webhook data for payment update
     *
     * @param string $asaasPaymentId
     * @param array $webhookData
     * @return array
     */
    public function processWebhook(string $asaasPaymentId, array $webhookData): array
    {
        try {
            $asaasSale = AsaasSale::where('asaas_payment_id', $asaasPaymentId)->first();

            if (!$asaasSale) {
                Log::warning('Webhook received for unknown payment', [
                    'asaas_payment_id' => $asaasPaymentId,
                    'webhook_data' => $webhookData
                ]);

                return [
                    'success' => false,
                    'message' => 'Payment not found',
                    'status' => 'payment_not_found'
                ];
            }

            // Update ASAAS sale with webhook data
            $asaasSale->updateFromWebhook($webhookData);

            Log::info('Payment updated from webhook', [
                'sale_id' => $asaasSale->sale_id,
                'asaas_payment_id' => $asaasPaymentId,
                'webhook_event' => $webhookData['event'] ?? 'unknown',
                'new_status' => $asaasSale->payment_status?->value
            ]);

            return [
                'success' => true,
                'message' => 'Payment updated from webhook',
                'status' => 'updated',
                'sale' => $asaasSale->sale->fresh(),
                'asaas_sale' => $asaasSale->fresh()
            ];

        } catch (\Exception $e) {
            Log::error('Failed to process payment webhook', [
                'asaas_payment_id' => $asaasPaymentId,
                'error' => $e->getMessage(),
                'webhook_data' => $webhookData
            ]);

            return [
                'success' => false,
                'message' => 'Webhook processing error: ' . $e->getMessage(),
                'status' => 'webhook_error'
            ];
        }
    }



    /**
     * Get sync summary for organization
     *
     * @param int $organizationId
     * @return array
     */
    public function getSyncSummary(int $organizationId): array
    {
        $totalSales = Sale::where('organization_id', $organizationId)
            ->whereHas('asaas')
            ->count();

        $syncedSales = Sale::where('organization_id', $organizationId)
            ->whereHas('asaas', function ($query) {
                $query->whereNotNull('asaas_synced_at')
                      ->where('asaas_synced_at', '>', now()->subHours(24));
            })
            ->count();

        $needsSyncSales = Sale::where('organization_id', $organizationId)
            ->whereHas('asaas', function ($query) {
                $query->where(function ($q) {
                    $q->whereNull('asaas_synced_at')
                      ->orWhere('asaas_synced_at', '<', now()->subHours(1));
                });
            })
            ->count();

        $errorSales = Sale::where('organization_id', $organizationId)
            ->whereHas('asaas', function ($query) {
                $query->whereNotNull('asaas_sync_errors');
            })
            ->count();

        return [
            'organization_id' => $organizationId,
            'total_sales_with_asaas' => $totalSales,
            'synced_last_24h' => $syncedSales,
            'needs_sync' => $needsSyncSales,
            'with_errors' => $errorSales,
            'sync_percentage' => $totalSales > 0 ? round(($syncedSales / $totalSales) * 100, 2) : 0,
        ];
    }
}
