<?php

namespace App\Services\ASAAS\UseCases\Deprecated\Subscriptions;

use App\Repositories\SubscriptionRepository;
use App\Services\ASAAS\AsaasService;
use App\Services\ASAAS\Repositories\AsaasSubscriptionRepository;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * Synchronizes ASAAS subscription data with local AsaasSubscription entities.
 * Handles subscription status updates, billing cycles, and error handling.
 */
class SyncAsaasSubscription
{
    private SubscriptionRepository $subscriptionRepository;
    private AsaasSubscriptionRepository $asaasSubscriptionRepository;
    private AsaasService $asaasService;

    public function __construct(
        SubscriptionRepository $subscriptionRepository,
        AsaasSubscriptionRepository $asaasSubscriptionRepository,
        AsaasService $asaasService
    ) {
        $this->subscriptionRepository = $subscriptionRepository;
        $this->asaasSubscriptionRepository = $asaasSubscriptionRepository;
        $this->asaasService = $asaasService;
    }

    public function perform(int $subscriptionId): array
    {
        DB::beginTransaction();

        try {
            // Buscar subscription
            $subscription = $this->subscriptionRepository->findById($subscriptionId);
            if (!$subscription) {
                throw new \InvalidArgumentException('Subscription not found');
            }

            // Buscar AsaasSubscription
            $asaasSubscription = $this->asaasSubscriptionRepository->findBySubscriptionId($subscriptionId);

            if (!$asaasSubscription) {
                throw new \InvalidArgumentException('ASAAS subscription not found');
            }

            // Buscar dados atualizados no ASAAS
            $asaasData = $this->asaasService->get('/v3/subscriptions/' . $asaasSubscription->asaas_subscription_id);

            // Atualizar status local baseado no ASAAS
            $this->asaasSubscriptionRepository->markAsSynced(
                $asaasSubscription->id,
                $asaasData['id']
            );

            DB::commit();

            Log::info('ASAAS subscription synced successfully', [
                'subscription_id' => $subscriptionId,
                'asaas_subscription_id' => $asaasSubscription->asaas_subscription_id,
            ]);

            return [
                'success' => true,
                'asaas_data' => $asaasData,
                'local_subscription' => $subscription->toArray(),
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to sync ASAAS subscription', [
                'subscription_id' => $subscriptionId,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }
}
