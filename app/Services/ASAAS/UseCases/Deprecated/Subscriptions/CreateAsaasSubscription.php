<?php

namespace App\Services\ASAAS\UseCases\Deprecated\Subscriptions;

use App\Repositories\SubscriptionRepository;
use App\Services\ASAAS\AsaasService;
use App\Services\ASAAS\Domains\AsaasSubscription;
use App\Services\ASAAS\Factories\AsaasSubscriptionFactory;
use App\Services\ASAAS\Repositories\AsaasSubscriptionRepository;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * Creates ASAAS subscriptions from Subscription entities.
 * Handles subscription creation, billing configuration, and local storage of subscription data.
 */
class CreateAsaasSubscription
{
    private SubscriptionRepository $subscriptionRepository;
    private AsaasSubscriptionRepository $asaasSubscriptionRepository;
    private AsaasSubscriptionFactory $asaasSubscriptionFactory;
    private AsaasService $asaasService;

    public function __construct(
        SubscriptionRepository $subscriptionRepository,
        AsaasSubscriptionRepository $asaasSubscriptionRepository,
        AsaasSubscriptionFactory $asaasSubscriptionFactory,
        AsaasService $asaasService
    ) {
        $this->subscriptionRepository = $subscriptionRepository;
        $this->asaasSubscriptionRepository = $asaasSubscriptionRepository;
        $this->asaasSubscriptionFactory = $asaasSubscriptionFactory;
        $this->asaasService = $asaasService;
    }

    public function perform(int $subscriptionId): AsaasSubscription
    {
        DB::beginTransaction();

        try {
            // Buscar subscription
            $subscription = $this->subscriptionRepository->findById($subscriptionId);
            if (!$subscription) {
                throw new \InvalidArgumentException('Subscription not found');
            }

            // 1. Criar subscription no ASAAS via API
            $asaasResponse = $this->asaasService->post('/v3/subscriptions', [
                'customer' => $subscription->organization_id, // Assumindo que customer já existe
                'billingType' => 'BOLETO',
                'value' => $subscription->value,
                'nextDueDate' => $subscription->expires_at?->format('Y-m-d'),
                'cycle' => 'MONTHLY',
                'description' => 'Assinatura do sistema',
            ]);

            // 2. Criar AsaasSubscription domain
            $asaasSubscription = $this->asaasSubscriptionFactory->buildFromStoreArray([
                'subscription_id' => $subscriptionId,
                'asaas_subscription_id' => $asaasResponse['id'],
                'asaas_customer_id' => $asaasResponse['customer'],
                'sync_status' => 'synced',
                'asaas_synced_at' => now(),
            ], $subscription);

            // 3. Salvar no banco
            $asaasSubscription = $this->asaasSubscriptionRepository->store($asaasSubscription);

            DB::commit();

            Log::info('ASAAS subscription created successfully', [
                'subscription_id' => $subscriptionId,
                'asaas_subscription_id' => $asaasResponse['id'],
            ]);

            return $asaasSubscription;

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to create ASAAS subscription', [
                'subscription_id' => $subscriptionId,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }
}
