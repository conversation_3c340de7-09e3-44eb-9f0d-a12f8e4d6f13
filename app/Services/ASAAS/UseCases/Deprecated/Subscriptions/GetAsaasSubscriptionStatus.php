<?php

namespace App\Services\ASAAS\UseCases\Deprecated\Subscriptions;

use App\Repositories\SubscriptionRepository;
use App\Services\ASAAS\Repositories\AsaasSubscriptionRepository;

/**
 * Retrieves ASAAS integration status for subscriptions.
 * Provides detailed information about sync status, errors, and integration health.
 */
class GetAsaasSubscriptionStatus
{
    private SubscriptionRepository $subscriptionRepository;
    private AsaasSubscriptionRepository $asaasSubscriptionRepository;

    public function __construct(
        SubscriptionRepository $subscriptionRepository,
        AsaasSubscriptionRepository $asaasSubscriptionRepository
    ) {
        $this->subscriptionRepository = $subscriptionRepository;
        $this->asaasSubscriptionRepository = $asaasSubscriptionRepository;
    }

    public function perform(int $subscriptionId): array
    {
        // Buscar subscription
        $subscription = $this->subscriptionRepository->findById($subscriptionId);
        if (!$subscription) {
            throw new \InvalidArgumentException('Subscription not found');
        }

        $asaasSubscription = $this->asaasSubscriptionRepository->findBySubscriptionId($subscriptionId);

        $status = [
            'subscription_id' => $subscriptionId,
            'subscription' => $subscription->toArray(),
            'has_asaas_integration' => $asaasSubscription !== null,
            'asaas_data' => null,
            'sync_status' => 'not_integrated',
        ];

        if ($asaasSubscription) {
            $status['asaas_data'] = $asaasSubscription->toArray();
            $status['sync_status'] = $asaasSubscription->isSynced() ? 'synced' :
                                   ($asaasSubscription->hasErrors() ? 'error' : 'pending');
        }

        return $status;
    }
}
