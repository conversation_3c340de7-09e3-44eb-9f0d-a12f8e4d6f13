<?php

namespace App\Services\ASAAS\UseCases\Deprecated\Clients;

use App\Services\ASAAS\Domains\AsaasClient;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Services\ASAAS\Repositories\AsaasClientRepository;
use Illuminate\Support\Facades\Log;

/**
 * Retrieves ASAAS clients by their customer ID.
 * Provides search and bulk operations for client lookup and statistics.
 */
class GetAsaasClientByCustomerId
{
    public function __construct(
        private AsaasClientRepository $asaasClientRepository
    ) {}

    /**
     * Get AsaasClient by ASAAS customer ID
     *
     * @param string $asaasCustomerId
     * @return AsaasClient
     * @throws AsaasException
     */
    public function perform(string $asaasCustomerId): AsaasClient
    {
        if (empty($asaasCustomerId)) {
            throw new AsaasException('ASAAS customer ID is required');
        }

        $asaasClient = $this->asaasClientRepository->findByAsaasCustomerId($asaasCustomerId);

        if (!$asaasClient) {
            throw new AsaasException('AsaasClient not found for ASAAS customer ID: ' . $asaasCustomerId);
        }

        Log::info('AsaasClient retrieved by customer ID', [
            'asaas_customer_id' => $asaasCustomerId,
            'asaas_client_id' => $asaasClient->id,
            'client_id' => $asaasClient->client_id,
            'organization_id' => $asaasClient->organization_id
        ]);

        return $asaasClient;
    }

    /**
     * Get AsaasClient by ASAAS customer ID with null return
     *
     * @param string $asaasCustomerId
     * @return AsaasClient|null
     */
    public function findByCustomerId(string $asaasCustomerId): ?AsaasClient
    {
        if (empty($asaasCustomerId)) {
            return null;
        }

        return $this->asaasClientRepository->findByAsaasCustomerId($asaasCustomerId);
    }

    /**
     * Check if AsaasClient exists by ASAAS customer ID
     *
     * @param string $asaasCustomerId
     * @return bool
     */
    public function exists(string $asaasCustomerId): bool
    {
        if (empty($asaasCustomerId)) {
            return false;
        }

        return $this->asaasClientRepository->existsByAsaasCustomerId($asaasCustomerId);
    }

    /**
     * Get AsaasClient with full details by ASAAS customer ID
     *
     * @param string $asaasCustomerId
     * @return array
     * @throws AsaasException
     */
    public function getWithDetails(string $asaasCustomerId): array
    {
        $asaasClient = $this->perform($asaasCustomerId);

        return [
            'asaas_client' => $asaasClient->toArray(),
            'client' => $asaasClient->client?->toArray(),
            'sync_status' => [
                'needs_sync' => $asaasClient->needsSync(),
                'has_errors' => $asaasClient->hasErrors(),
                'sync_status' => $asaasClient->sync_status,
                'last_synced_at' => $asaasClient->asaas_synced_at?->format('Y-m-d H:i:s'),
                'sync_errors' => $asaasClient->asaas_sync_errors,
            ],
            'integration_status' => [
                'has_asaas_integration' => $asaasClient->hasAsaasIntegration(),
                'is_active' => $asaasClient->isActive(),
                'is_deleted' => $asaasClient->isDeleted(),
                'person_type' => $asaasClient->person_type,
                'has_valid_document' => $asaasClient->hasValidDocument(),
                'has_valid_email' => $asaasClient->hasValidEmail(),
                'has_valid_phone' => $asaasClient->hasValidPhone(),
            ]
        ];
    }

    /**
     * Get multiple AsaasClients by ASAAS customer IDs
     *
     * @param array $asaasCustomerIds
     * @return array
     */
    public function getMultipleByCustomerIds(array $asaasCustomerIds): array
    {
        $results = [];
        $notFound = [];

        foreach ($asaasCustomerIds as $asaasCustomerId) {
            try {
                $asaasClient = $this->perform($asaasCustomerId);
                $results[$asaasCustomerId] = $asaasClient;
            } catch (AsaasException $e) {
                $notFound[] = $asaasCustomerId;

                Log::warning('AsaasClient not found in bulk retrieval', [
                    'asaas_customer_id' => $asaasCustomerId,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return [
            'found' => $results,
            'not_found' => $notFound,
            'total_found' => count($results),
            'total_not_found' => count($notFound)
        ];
    }

    /**
     * Search AsaasClients by partial customer ID
     *
     * @param string $partialCustomerId
     * @param int $limit
     * @return array
     */
    public function searchByPartialCustomerId(string $partialCustomerId, int $limit = 10): array
    {
        if (strlen($partialCustomerId) < 3) {
            throw new AsaasException('Search term must be at least 3 characters long');
        }

        $filters = [
            'asaas_customer_id_like' => $partialCustomerId,
        ];

        $asaasClients = $this->asaasClientRepository->search($filters, $limit);

        Log::info('AsaasClients searched by partial customer ID', [
            'search_term' => $partialCustomerId,
            'results_count' => $asaasClients->count(),
            'limit' => $limit
        ]);

        return $asaasClients->toArray();
    }

    /**
     * Get AsaasClient statistics by customer ID pattern
     *
     * @param string $customerIdPattern
     * @return array
     */
    public function getStatsByPattern(string $customerIdPattern): array
    {
        $filters = [
            'asaas_customer_id_like' => $customerIdPattern,
        ];

        $asaasClients = $this->asaasClientRepository->search($filters, 1000);

        $stats = [
            'total_count' => $asaasClients->count(),
            'active_count' => $asaasClients->filter(fn($client) => $client->isActive())->count(),
            'deleted_count' => $asaasClients->filter(fn($client) => $client->isDeleted())->count(),
            'synced_count' => $asaasClients->filter(fn($client) => $client->isSynced())->count(),
            'pending_count' => $asaasClients->filter(fn($client) => $client->isPending())->count(),
            'error_count' => $asaasClients->filter(fn($client) => $client->hasError())->count(),
            'needs_sync_count' => $asaasClients->filter(fn($client) => $client->needsSync())->count(),
        ];

        Log::info('AsaasClient statistics generated', [
            'pattern' => $customerIdPattern,
            'stats' => $stats
        ]);

        return $stats;
    }
}
