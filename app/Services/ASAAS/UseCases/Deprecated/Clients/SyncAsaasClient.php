<?php

namespace App\Services\ASAAS\UseCases\Deprecated\Clients;

use App\Enums\AsaasEnvironment;
use App\Services\ASAAS\AsaasService;
use App\Services\ASAAS\Domains\AsaasClient;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Services\ASAAS\Factories\AsaasClientFactory;
use App\Services\ASAAS\Repositories\AsaasClientRepository;
use App\Services\ASAAS\Repositories\AsaasOrganizationRepository;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * Synchronizes ASAAS client data with local AsaasClient entities.
 * Handles customer data updates, bulk synchronization, and error handling.
 */
class SyncAsaasClient
{
    public function __construct(
        private AsaasService $asaasService,
        private AsaasClientRepository $asaasClientRepository,
        private AsaasOrganizationRepository $asaasOrganizationRepository,
        private AsaasClientFactory $asaasClientFactory
    ) {}

    /**
     * Sync AsaasClient data from ASAAS API
     *
     * @param AsaasClient $asaasClient
     * @param AsaasEnvironment|null $environment
     * @return AsaasClient
     * @throws AsaasException
     */
    public function perform(AsaasClient $asaasClient, ?AsaasEnvironment $environment = null): AsaasClient
    {
        if (!$asaasClient->hasAsaasIntegration()) {
            throw new AsaasException('AsaasClient does not have ASAAS customer ID');
        }

        // Get organization for API key
        $organization = $this->asaasOrganizationRepository->findByOrganizationId($asaasClient->organization_id);
        if (!$organization || !$organization->hasAsaasIntegration()) {
            throw new AsaasException('Organization must have ASAAS integration');
        }

        DB::beginTransaction();

        try {
            // Get customer data from ASAAS
            $asaasResponse = $this->asaasService->get(
                "/v3/customers/{$asaasClient->asaas_customer_id}",
                [],
                $organization->asaas_api_key,
                $environment ?: AsaasEnvironment::SANDBOX
            );

            // Build updated AsaasClient from response
            $updatedAsaasClient = $this->asaasClientFactory->buildFromAsaasResponse(
                $asaasResponse,
                $asaasClient->client_id,
                $asaasClient->organization_id
            );

            // Update in database
            $savedAsaasClient = $this->asaasClientRepository->update($updatedAsaasClient);

            // Mark as synced
            $this->asaasClientRepository->markAsSynced($savedAsaasClient->id, $asaasResponse);

            DB::commit();

            Log::info('ASAAS customer synced successfully', [
                'asaas_client_id' => $asaasClient->id,
                'asaas_customer_id' => $asaasClient->asaas_customer_id,
                'organization_id' => $asaasClient->organization_id
            ]);

            return $savedAsaasClient;

        } catch (AsaasException $e) {
            DB::rollBack();

            // Mark sync error
            $errorData = [
                'error' => $e->getMessage(),
                'timestamp' => now()->toISOString(),
                'operation' => 'sync',
            ];

            $this->asaasClientRepository->markSyncError($asaasClient->id, $errorData);

            Log::error('Failed to sync ASAAS customer', [
                'asaas_client_id' => $asaasClient->id,
                'asaas_customer_id' => $asaasClient->asaas_customer_id,
                'error' => $e->getMessage()
            ]);

            throw $e;

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to sync ASAAS customer', [
                'asaas_client_id' => $asaasClient->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw new AsaasException('Failed to sync customer: ' . $e->getMessage());
        }
    }

    /**
     * Sync AsaasClient by client ID
     *
     * @param int $clientId
     * @param AsaasEnvironment|null $environment
     * @return AsaasClient
     * @throws AsaasException
     */
    public function performByClientId(int $clientId, ?AsaasEnvironment $environment = null): AsaasClient
    {
        $asaasClient = $this->asaasClientRepository->findByClientId($clientId);
        if (!$asaasClient) {
            throw new AsaasException('AsaasClient not found for client ID: ' . $clientId);
        }

        return $this->perform($asaasClient, $environment);
    }

    /**
     * Sync AsaasClient by ASAAS customer ID
     *
     * @param string $asaasCustomerId
     * @param AsaasEnvironment|null $environment
     * @return AsaasClient
     * @throws AsaasException
     */
    public function performByAsaasCustomerId(string $asaasCustomerId, ?AsaasEnvironment $environment = null): AsaasClient
    {
        $asaasClient = $this->asaasClientRepository->findByAsaasCustomerId($asaasCustomerId);
        if (!$asaasClient) {
            throw new AsaasException('AsaasClient not found for ASAAS customer ID: ' . $asaasCustomerId);
        }

        return $this->perform($asaasClient, $environment);
    }

    /**
     * Sync all AsaasClients that need sync
     *
     * @param AsaasEnvironment|null $environment
     * @return array
     */
    public function syncAllNeedingSync(?AsaasEnvironment $environment = null): array
    {
        $asaasClients = $this->asaasClientRepository->getNeedingSync();

        Log::info('Starting bulk sync for AsaasClients', [
            'total_clients' => $asaasClients->count()
        ]);

        return $this->bulkSync($asaasClients->toArray(), $environment);
    }

    /**
     * Sync AsaasClients by organization
     *
     * @param int $organizationId
     * @param AsaasEnvironment|null $environment
     * @return array
     */
    public function syncByOrganization(int $organizationId, ?AsaasEnvironment $environment = null): array
    {
        $asaasClients = $this->asaasClientRepository->getByOrganizationId($organizationId);

        Log::info('Starting sync for organization AsaasClients', [
            'organization_id' => $organizationId,
            'total_clients' => $asaasClients->count()
        ]);

        return $this->bulkSync($asaasClients->toArray(), $environment);
    }

    /**
     * Bulk sync multiple AsaasClients
     *
     * @param array $asaasClients
     * @param AsaasEnvironment|null $environment
     * @return array
     */
    public function bulkSync(array $asaasClients, ?AsaasEnvironment $environment = null): array
    {
        $results = [];
        $errors = [];

        foreach ($asaasClients as $asaasClient) {
            try {
                if (!$asaasClient instanceof AsaasClient) {
                    $errors[$asaasClient['id'] ?? 'unknown'] = 'Invalid AsaasClient object';
                    continue;
                }

                $result = $this->perform($asaasClient, $environment);
                $results[$asaasClient->id] = $result;

            } catch (\Exception $e) {
                $errors[$asaasClient->id ?? 'unknown'] = $e->getMessage();

                Log::error('Failed to sync ASAAS customer in bulk', [
                    'asaas_client_id' => $asaasClient->id ?? 'unknown',
                    'error' => $e->getMessage()
                ]);
            }
        }

        Log::info('Bulk sync completed', [
            'total_processed' => count($results),
            'total_errors' => count($errors)
        ]);

        return [
            'results' => $results,
            'errors' => $errors,
            'total_processed' => count($results),
            'total_errors' => count($errors)
        ];
    }

    /**
     * Force sync AsaasClient (ignore last sync time)
     *
     * @param AsaasClient $asaasClient
     * @param AsaasEnvironment|null $environment
     * @return AsaasClient
     * @throws AsaasException
     */
    public function forceSync(AsaasClient $asaasClient, ?AsaasEnvironment $environment = null): AsaasClient
    {
        Log::info('Force syncing ASAAS customer', [
            'asaas_client_id' => $asaasClient->id,
            'asaas_customer_id' => $asaasClient->asaas_customer_id
        ]);

        return $this->perform($asaasClient, $environment);
    }

    /**
     * Check if AsaasClient needs sync
     *
     * @param AsaasClient $asaasClient
     * @return bool
     */
    public function needsSync(AsaasClient $asaasClient): bool
    {
        return $asaasClient->needsSync();
    }

    /**
     * Get sync status for AsaasClient
     *
     * @param AsaasClient $asaasClient
     * @return array
     */
    public function getSyncStatus(AsaasClient $asaasClient): array
    {
        return [
            'needs_sync' => $asaasClient->needsSync(),
            'has_errors' => $asaasClient->hasErrors(),
            'sync_status' => $asaasClient->sync_status,
            'last_synced_at' => $asaasClient->asaas_synced_at?->format('Y-m-d H:i:s'),
            'sync_errors' => $asaasClient->asaas_sync_errors,
        ];
    }
}
