<?php

namespace App\Services\ASAAS\UseCases\Deprecated\Clients;

use App\Enums\AsaasEnvironment;
use App\Services\ASAAS\AsaasService;
use App\Services\ASAAS\Domains\AsaasClient;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Services\ASAAS\Repositories\AsaasClientRepository;
use App\Services\ASAAS\Repositories\AsaasOrganizationRepository;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * Deletes ASAAS clients both locally and in the ASAAS API.
 * Handles soft deletion and provides bulk operations for client management.
 */
class DeleteAsaasClient
{
    public function __construct(
        private AsaasService $asaasService,
        private AsaasClientRepository $asaasClientRepository,
        private AsaasOrganizationRepository $asaasOrganizationRepository
    ) {}

    /**
     * Delete AsaasClient (soft delete locally and mark as deleted in ASAAS)
     *
     * @param AsaasClient $asaasClient
     * @param AsaasEnvironment|null $environment
     * @return bool
     * @throws AsaasException
     */
    public function perform(AsaasClient $asaasClient, ?AsaasEnvironment $environment = null): bool
    {
        if (!$asaasClient->hasAsaasIntegration()) {
            throw new AsaasException('AsaasClient does not have ASAAS customer ID');
        }

        // Get organization for API key
        $organization = $this->asaasOrganizationRepository->findByOrganizationId($asaasClient->organization_id);
        if (!$organization || !$organization->hasAsaasIntegration()) {
            throw new AsaasException('Organization must have ASAAS integration');
        }

        DB::beginTransaction();

        try {
            // Delete customer in ASAAS (this marks as deleted, doesn't actually remove)
            $this->asaasService->delete(
                "/v3/customers/{$asaasClient->asaas_customer_id}",
                $organization->asaas_api_key,
                $environment ?: AsaasEnvironment::SANDBOX
            );

            // Soft delete AsaasClient locally
            $deleted = $this->asaasClientRepository->delete($asaasClient->id);

            DB::commit();

            Log::info('ASAAS customer deleted successfully', [
                'asaas_client_id' => $asaasClient->id,
                'asaas_customer_id' => $asaasClient->asaas_customer_id,
                'organization_id' => $asaasClient->organization_id
            ]);

            return $deleted;

        } catch (AsaasException $e) {
            DB::rollBack();

            Log::error('Failed to delete ASAAS customer', [
                'asaas_client_id' => $asaasClient->id,
                'asaas_customer_id' => $asaasClient->asaas_customer_id,
                'error' => $e->getMessage()
            ]);

            throw $e;

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to delete ASAAS customer', [
                'asaas_client_id' => $asaasClient->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw new AsaasException('Failed to delete customer: ' . $e->getMessage());
        }
    }

    /**
     * Delete AsaasClient by client ID
     *
     * @param int $clientId
     * @param AsaasEnvironment|null $environment
     * @return bool
     * @throws AsaasException
     */
    public function performByClientId(int $clientId, ?AsaasEnvironment $environment = null): bool
    {
        $asaasClient = $this->asaasClientRepository->findByClientId($clientId);
        if (!$asaasClient) {
            throw new AsaasException('AsaasClient not found for client ID: ' . $clientId);
        }

        return $this->perform($asaasClient, $environment);
    }

    /**
     * Delete AsaasClient by ASAAS customer ID
     *
     * @param string $asaasCustomerId
     * @param AsaasEnvironment|null $environment
     * @return bool
     * @throws AsaasException
     */
    public function performByAsaasCustomerId(string $asaasCustomerId, ?AsaasEnvironment $environment = null): bool
    {
        $asaasClient = $this->asaasClientRepository->findByAsaasCustomerId($asaasCustomerId);
        if (!$asaasClient) {
            throw new AsaasException('AsaasClient not found for ASAAS customer ID: ' . $asaasCustomerId);
        }

        return $this->perform($asaasClient, $environment);
    }

    /**
     * Soft delete AsaasClient only (without calling ASAAS API)
     *
     * @param AsaasClient $asaasClient
     * @return bool
     * @throws AsaasException
     */
    public function softDeleteOnly(AsaasClient $asaasClient): bool
    {
        DB::beginTransaction();

        try {
            $deleted = $this->asaasClientRepository->delete($asaasClient->id);

            DB::commit();

            Log::info('AsaasClient soft deleted locally', [
                'asaas_client_id' => $asaasClient->id,
                'asaas_customer_id' => $asaasClient->asaas_customer_id,
                'organization_id' => $asaasClient->organization_id
            ]);

            return $deleted;

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to soft delete AsaasClient', [
                'asaas_client_id' => $asaasClient->id,
                'error' => $e->getMessage()
            ]);

            throw new AsaasException('Failed to soft delete AsaasClient: ' . $e->getMessage());
        }
    }

    /**
     * Bulk delete multiple AsaasClients
     *
     * @param array $asaasClientIds
     * @param AsaasEnvironment|null $environment
     * @return array
     */
    public function bulkDelete(array $asaasClientIds, ?AsaasEnvironment $environment = null): array
    {
        $results = [];
        $errors = [];

        foreach ($asaasClientIds as $asaasClientId) {
            try {
                $asaasClient = $this->asaasClientRepository->findById($asaasClientId);
                if (!$asaasClient) {
                    $errors[$asaasClientId] = 'AsaasClient not found';
                    continue;
                }

                $result = $this->perform($asaasClient, $environment);
                $results[$asaasClientId] = $result;

            } catch (\Exception $e) {
                $errors[$asaasClientId] = $e->getMessage();

                Log::error('Failed to delete ASAAS customer in bulk', [
                    'asaas_client_id' => $asaasClientId,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return [
            'results' => $results,
            'errors' => $errors,
            'total_processed' => count($results),
            'total_errors' => count($errors)
        ];
    }

    /**
     * Check if AsaasClient can be deleted
     *
     * @param AsaasClient $asaasClient
     * @return bool
     */
    public function canDelete(AsaasClient $asaasClient): bool
    {
        // Check if already deleted
        if ($asaasClient->isDeleted()) {
            return false;
        }

        // Check if has ASAAS integration
        if (!$asaasClient->hasAsaasIntegration()) {
            return false;
        }

        // Add additional business rules here if needed
        // For example: check if client has pending payments, etc.

        return true;
    }

    /**
     * Get deletion status for AsaasClient
     *
     * @param AsaasClient $asaasClient
     * @return array
     */
    public function getDeletionStatus(AsaasClient $asaasClient): array
    {
        return [
            'can_delete' => $this->canDelete($asaasClient),
            'is_deleted' => $asaasClient->isDeleted(),
            'has_asaas_integration' => $asaasClient->hasAsaasIntegration(),
            'deleted_at' => $asaasClient->deleted_at?->format('Y-m-d H:i:s'),
        ];
    }
}
