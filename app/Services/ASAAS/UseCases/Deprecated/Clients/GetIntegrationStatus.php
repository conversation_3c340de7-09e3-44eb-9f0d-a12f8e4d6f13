<?php

namespace App\Services\ASAAS\UseCases\Deprecated\Clients;

use App\Domains\Inventory\Client as ClientDomain;
use App\Services\ASAAS\Repositories\AsaasClientRepository;

/**
 * Retrieves ASAAS integration status for clients.
 * Provides detailed information about sync status, errors, and integration health.
 */
class GetIntegrationStatus
{
    protected AsaasClientRepository $asaasClientRepository;

    public function __construct(AsaasClientRepository $asaasClientRepository)
    {
        $this->asaasClientRepository = $asaasClientRepository;
    }

    /**
     * Get ASAAS integration status for client
     *
     * @param ClientDomain $client
     * @return array
     */
    public function perform(ClientDomain $client): array
    {
        $asaasClient = $this->asaasClientRepository->findByClientId($client->id);

        $status = [
            'client_id' => $client->id,
            'client_name' => $client->name,
            'organization_id' => $client->organization_id,
            'has_asaas_integration' => $asaasClient !== null,
            'asaas_data' => null,
            'sync_status' => 'not_integrated'
        ];

        if ($asaasClient) {
            $status['asaas_data'] = [
                'asaas_customer_id' => $asaasClient->asaas_customer_id,
                'asaas_synced_at' => $asaasClient->asaas_synced_at?->toDateTimeString(),
                'asaas_sync_errors' => $asaasClient->asaas_sync_errors,
                'created_at' => $asaasClient->created_at?->toDateTimeString(),
                'updated_at' => $asaasClient->updated_at?->toDateTimeString(),
            ];

            // Determine sync status
            if ($asaasClient->asaas_sync_errors) {
                $status['sync_status'] = 'error';
            } elseif ($asaasClient->asaas_customer_id) {
                if ($asaasClient->asaas_synced_at && $asaasClient->asaas_synced_at->isAfter(now()->subHour())) {
                    $status['sync_status'] = 'synced';
                } else {
                    $status['sync_status'] = 'needs_sync';
                }
            } else {
                $status['sync_status'] = 'pending';
            }
        }

        return $status;
    }
}
