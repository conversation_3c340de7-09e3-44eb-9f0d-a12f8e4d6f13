<?php

namespace App\Services\ASAAS\UseCases\Deprecated\Clients;

use App\Enums\AsaasEnvironment;
use App\Services\ASAAS\AsaasService;
use App\Services\ASAAS\Domains\AsaasClient;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Services\ASAAS\Factories\AsaasClientFactory;
use App\Services\ASAAS\Repositories\AsaasClientRepository;
use App\Services\ASAAS\Repositories\AsaasOrganizationRepository;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * Updates ASAAS clients both locally and in the ASAAS API.
 * Handles customer data updates, validation, and synchronization.
 */
class UpdateAsaasClient
{
    public function __construct(
        private AsaasService $asaasService,
        private AsaasClientRepository $asaasClientRepository,
        private AsaasOrganizationRepository $asaasOrganizationRepository,
        private AsaasClientFactory $asaasClientFactory
    ) {}

    /**
     * Update AsaasClient data in ASAAS API and local database
     *
     * @param AsaasClient $asaasClient
     * @param AsaasEnvironment|null $environment
     * @return AsaasClient
     * @throws AsaasException
     */
    public function perform(AsaasClient $asaasClient, ?AsaasEnvironment $environment = null): AsaasClient
    {
        if (!$asaasClient->hasAsaasIntegration()) {
            throw new AsaasException('AsaasClient does not have ASAAS customer ID');
        }

        // Get organization for API key
        $organization = $this->asaasOrganizationRepository->findByOrganizationId($asaasClient->organization_id);
        if (!$organization || !$organization->hasAsaasIntegration()) {
            throw new AsaasException('Organization must have ASAAS integration');
        }

        DB::beginTransaction();

        try {
            // Prepare customer data for ASAAS API
            $customerData = $asaasClient->toAsaasPayload();

            // Update customer in ASAAS
            $asaasResponse = $this->asaasService->put(
                "/v3/customers/{$asaasClient->asaas_customer_id}",
                $customerData,
                $organization->asaas_api_key,
                $environment ?: AsaasEnvironment::SANDBOX
            );

            // Update AsaasClient with response data
            $updatedAsaasClient = $this->asaasClientFactory->buildFromAsaasResponse(
                $asaasResponse,
                $asaasClient->client_id,
                $asaasClient->organization_id
            );

            // Update in database
            $savedAsaasClient = $this->asaasClientRepository->update($updatedAsaasClient);

            DB::commit();

            Log::info('ASAAS customer updated successfully', [
                'asaas_client_id' => $asaasClient->id,
                'asaas_customer_id' => $asaasClient->asaas_customer_id,
                'organization_id' => $asaasClient->organization_id
            ]);

            return $savedAsaasClient;

        } catch (AsaasException $e) {
            DB::rollBack();

            // Mark sync error
            $errorData = [
                'error' => $e->getMessage(),
                'timestamp' => now()->toISOString(),
                'operation' => 'update',
            ];

            $this->asaasClientRepository->markSyncError($asaasClient->id, $errorData);

            Log::error('Failed to update ASAAS customer', [
                'asaas_client_id' => $asaasClient->id,
                'asaas_customer_id' => $asaasClient->asaas_customer_id,
                'error' => $e->getMessage()
            ]);

            throw $e;

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to update ASAAS customer', [
                'asaas_client_id' => $asaasClient->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw new AsaasException('Failed to update customer: ' . $e->getMessage());
        }
    }

    /**
     * Update AsaasClient by client ID
     *
     * @param int $clientId
     * @param AsaasEnvironment|null $environment
     * @return AsaasClient
     * @throws AsaasException
     */
    public function performByClientId(int $clientId, ?AsaasEnvironment $environment = null): AsaasClient
    {
        $asaasClient = $this->asaasClientRepository->findByClientId($clientId);
        if (!$asaasClient) {
            throw new AsaasException('AsaasClient not found for client ID: ' . $clientId);
        }

        return $this->perform($asaasClient, $environment);
    }

    /**
     * Update AsaasClient by ASAAS customer ID
     *
     * @param string $asaasCustomerId
     * @param AsaasEnvironment|null $environment
     * @return AsaasClient
     * @throws AsaasException
     */
    public function performByAsaasCustomerId(string $asaasCustomerId, ?AsaasEnvironment $environment = null): AsaasClient
    {
        $asaasClient = $this->asaasClientRepository->findByAsaasCustomerId($asaasCustomerId);
        if (!$asaasClient) {
            throw new AsaasException('AsaasClient not found for ASAAS customer ID: ' . $asaasCustomerId);
        }

        return $this->perform($asaasClient, $environment);
    }

    /**
     * Bulk update multiple AsaasClients
     *
     * @param array $asaasClientIds
     * @param AsaasEnvironment|null $environment
     * @return array
     */
    public function bulkUpdate(array $asaasClientIds, ?AsaasEnvironment $environment = null): array
    {
        $results = [];
        $errors = [];

        foreach ($asaasClientIds as $asaasClientId) {
            try {
                $asaasClient = $this->asaasClientRepository->findById($asaasClientId);
                if (!$asaasClient) {
                    $errors[$asaasClientId] = 'AsaasClient not found';
                    continue;
                }

                $result = $this->perform($asaasClient, $environment);
                $results[$asaasClientId] = $result;

            } catch (\Exception $e) {
                $errors[$asaasClientId] = $e->getMessage();

                Log::error('Failed to update ASAAS customer in bulk', [
                    'asaas_client_id' => $asaasClientId,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return [
            'results' => $results,
            'errors' => $errors,
            'total_processed' => count($results),
            'total_errors' => count($errors)
        ];
    }

    /**
     * Validate AsaasClient data before update
     *
     * @param AsaasClient $asaasClient
     * @throws AsaasException
     */
    protected function validateAsaasClientData(AsaasClient $asaasClient): void
    {
        $errors = [];

        if (empty($asaasClient->name)) {
            $errors[] = 'Name is required';
        }

        if (empty($asaasClient->email) || !$asaasClient->hasValidEmail()) {
            $errors[] = 'Valid email is required';
        }

        if (empty($asaasClient->cpf_cnpj) || !$asaasClient->hasValidDocument()) {
            $errors[] = 'Valid document (CPF/CNPJ) is required';
        }

        if (!empty($errors)) {
            throw new AsaasException('Validation failed: ' . implode(', ', $errors));
        }
    }
}
