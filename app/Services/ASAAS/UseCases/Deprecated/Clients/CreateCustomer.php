<?php

namespace App\Services\ASAAS\UseCases\Deprecated\Clients;

use App\Domains\Inventory\Client;
use App\Enums\AsaasEnvironment;
use App\Repositories\ClientRepository;
use App\Services\ASAAS\AsaasService;
use App\Services\ASAAS\Domains\AsaasClient as AsaasClientDomain;
use App\Services\ASAAS\Domains\AsaasOrganization;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Services\ASAAS\Factories\AsaasClientFactory;
use App\Services\ASAAS\Repositories\AsaasClientRepository;
use App\Services\ASAAS\Repositories\AsaasOrganizationRepository;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * Creates ASAAS customers from Client entities.
 * Handles validation, API communication, and local storage of customer data.
 */
class CreateCustomer
{
    public function __construct(
        private AsaasService $asaasService,
        private AsaasClientRepository $asaasClientRepository,
        private AsaasOrganizationRepository $asaasOrganizationRepository,
        private AsaasClientFactory $asaasClientFactory,
        private ClientRepository $clientRepository
    ) {}

    /**
     * Create customer in ASAAS and AsaasClient entity
     *
     * @param Client $client
     * @param AsaasEnvironment|null $environment
     * @return AsaasClientDomain
     * @throws AsaasException
     */
    public function perform(Client $client, ?AsaasEnvironment $environment = null): AsaasClientDomain
    {
        // Check if client already has AsaasClient
        $existingAsaasClient = $this->asaasClientRepository->findByClientId($client->id);
        if ($existingAsaasClient) {
            throw new AsaasException('Client already has an ASAAS customer integration');
        }

        // Validate required fields and get organization
        $organization = $this->validateClientData($client);

        DB::beginTransaction();

        try {
            // Prepare customer data for ASAAS API
            $customerData = $this->prepareCustomerData($client);

            // Create customer in ASAAS
            $asaasResponse = $this->asaasService->post(
                '/v3/customers',
                $customerData,
                $organization->asaas_api_key,
                $environment ?: AsaasEnvironment::SANDBOX
            );

            // Create AsaasClient domain from response data
            $asaasClientDomain = $this->asaasClientFactory->buildFromAsaasResponse(
                $asaasResponse,
                $client->id,
                $client->organization_id
            );

            // Save AsaasClient
            $savedAsaasClient = $this->asaasClientRepository->create($asaasClientDomain);

            DB::commit();

            Log::info('ASAAS customer created successfully', [
                'client_id' => $client->id,
                'asaas_customer_id' => $asaasResponse['id'],
                'organization_id' => $client->organization_id
            ]);

            return $savedAsaasClient;

        } catch (AsaasException $e) {
            DB::rollBack();

            Log::error('Failed to create ASAAS customer', [
                'client_id' => $client->id,
                'error' => $e->getMessage(),
                'organization_id' => $client->organization_id
            ]);

            throw $e;

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to create ASAAS customer', [
                'client_id' => $client->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw new AsaasException('Failed to create customer: ' . $e->getMessage());
        }
    }

    /**
     * Validate client data for ASAAS customer creation
     *
     * @param Client $client
     * @return AsaasOrganization
     * @throws AsaasException
     */
    protected function validateClientData(Client $client): AsaasOrganization
    {
        $errors = [];

        if (empty($client->name)) {
            $errors[] = 'Client name is required';
        }

        if (empty($client->email)) {
            $errors[] = 'Client email is required';
        }

        $document = $client->cpf ?: $client->cnpj;
        if (empty($document)) {
            $errors[] = 'Client document (CPF/CNPJ) is required';
        }

        if (!empty($errors)) {
            throw new AsaasException('Validation failed: ' . implode(', ', $errors));
        }

        // Validate organization has ASAAS integration
        $organization = $this->asaasOrganizationRepository->findByOrganizationId($client->organization_id);
        if (!$organization || !$organization->hasAsaasIntegration()) {
            throw new AsaasException('Organization must have ASAAS integration before creating customers');
        }

        return $organization;
    }

    /**
     * Prepare customer data for ASAAS API
     *
     * @param Client $client
     * @return array
     */
    protected function prepareCustomerData(Client $client): array
    {
        $document = $client->cpf ?: $client->cnpj;
        $data = [
            'name' => $client->name,
            'email' => $client->email,
            'cpfCnpj' => $this->cleanDocument($document),
        ];

        // Add optional fields if available
        if ($client->phone) {
            $data['phone'] = $client->phone;
        }

        if ($client->phone) {
            $data['mobilePhone'] = $client->phone; // Use same phone as mobile
        }

        if ($client->cep) {
            $data['postalCode'] = $this->cleanPostalCode($client->cep);
        }

        if ($client->address) {
            $data['address'] = $client->address;
        }

        if ($client->number) {
            $data['addressNumber'] = $client->number;
        }

        if ($client->complement) {
            $data['complement'] = $client->complement;
        }

        if ($client->neighborhood) {
            $data['province'] = $client->neighborhood;
        }

        if ($client->birthdate) {
            $data['birthDate'] = $client->birthDate()->format('Y-m-d');
        }

        // Add external reference for tracking
        $data['externalReference'] = "client_{$client->id}";

        return $data;
    }

    /**
     * Clean document (remove special characters)
     *
     * @param string $document
     * @return string
     */
    protected function cleanDocument(string $document): string
    {
        return preg_replace('/[^0-9]/', '', $document);
    }

    /**
     * Clean postal code (remove special characters)
     *
     * @param string $postalCode
     * @return string
     */
    protected function cleanPostalCode(string $postalCode): string
    {
        return preg_replace('/[^0-9]/', '', $postalCode);
    }

    /**
     * Check if client can create ASAAS customer
     *
     * @param Client $client
     * @return bool
     */
    public function canCreateCustomer(Client $client): bool
    {
        // Already has ASAAS customer
        if ($client->hasAsaasIntegration()) {
            return false;
        }

        // Organization must have ASAAS integration
        $organization = $this->asaasOrganizationRepository->findByOrganizationId($client->organization_id);
        if (!$organization || !$organization->hasAsaasIntegration()) {
            return false;
        }

        // Check required fields
        if (empty($client->name) || empty($client->email) || empty($client->getDocument())) {
            return false;
        }

        return true;
    }

    /**
     * Bulk create customers for multiple clients
     *
     * @param array $clientIds
     * @param AsaasEnvironment|null $environment
     * @return array
     */
    public function bulkCreate(array $clientIds, ?AsaasEnvironment $environment = null): array
    {
        $results = [];
        $errors = [];

        foreach ($clientIds as $clientId) {
            try {
                $client = $this->clientRepository->fetchById($clientId);

                if (!$this->canCreateCustomer($client)) {
                    $errors[$clientId] = [
                        'error' => 'Client cannot create ASAAS customer',
                        'client_id' => $clientId
                    ];
                    continue;
                }

                $result = $this->perform($client, $environment);
                $results[$clientId] = $result;

            } catch (\Exception $e) {
                $errors[$clientId] = [
                    'error' => $e->getMessage(),
                    'client_id' => $clientId
                ];

                Log::error('Failed to create ASAAS customer in bulk', [
                    'client_id' => $clientId,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return [
            'results' => $results,
            'errors' => $errors,
            'total_processed' => count($results),
            'total_errors' => count($errors)
        ];
    }

    /**
     * Create customers for all clients without ASAAS integration
     * Note: This method should be called from a repository or service layer
     * that provides the client IDs, not directly querying models
     *
     * @param array $clientIds
     * @param AsaasEnvironment|null $environment
     * @return array
     */
    public function createForClientIds(array $clientIds, ?AsaasEnvironment $environment = null): array
    {
        Log::info('Starting bulk customer creation', [
            'total_clients' => count($clientIds)
        ]);

        return $this->bulkCreate($clientIds, $environment);
    }
}
