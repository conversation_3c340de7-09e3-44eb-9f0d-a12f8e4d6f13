<?php

namespace App\Services\ASAAS\UseCases\Account;

use App\Services\ASAAS\AccountService;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Services\ASAAS\Repositories\AsaasOrganizationRepository;
use App\Domains\Organization as OrganizationDomain;
use Throwable;

class GetSubaccount
{
    private AccountService $accountService;
    private AsaasOrganizationRepository $asaasOrganizationRepository;

    public function __construct(
        AccountService $accountService,
        AsaasOrganizationRepository $asaasOrganizationRepository
    ) {
        $this->accountService = $accountService;
        $this->asaasOrganizationRepository = $asaasOrganizationRepository;
    }

    public function perform(OrganizationDomain $organization): array
    {
        if (!$organization->hasAsaasIntegration()) {
            return [
                'found' => false,
                'error' => 'Organization does not have ASAAS integration configured',
                'asaas_account_id' => null,
                'asaas_data' => null,
                'organization' => $organization,
            ];
        }
        try {
            $response = $this->accountService->getSubaccount($organization->asaas_account_id ?? $organization->asaas->asaas_account_id);

            return [
                'found' => true,
                'error' => null,
                'asaas_account_id' => $organization->asaas_account_id,
                'asaas_data' => $response,
                'organization' => $organization,
            ];

        } catch (Throwable $e) {
            return [
                'found' => false,
                'error' => "Failed to get subaccount: {$e->getMessage()}",
                'asaas_account_id' => $organization->asaas_account_id,
                'asaas_data' => null,
                'organization' => $organization,
            ];
        }
    }
}
