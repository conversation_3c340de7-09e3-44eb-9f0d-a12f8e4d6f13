<?php

namespace App\Services\ASAAS\UseCases\Account;

use App\Services\ASAAS\AccountService;
use App\Services\ASAAS\Domains\AsaasOrganization;
use App\Services\ASAAS\Factories\AsaasOrganizationFactory;
use App\Services\ASAAS\Repositories\AsaasOrganizationRepository;
use App\Services\ASAAS\UseCases\AsaasLog\LogAsaasRequest;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Repositories\OrganizationRepository;
use App\Domains\Organization as OrganizationDomain;
use App\Helpers\DBLog;
use Illuminate\Support\Facades\DB;
use Throwable;

class SyncSubaccount
{
    public function __construct(
        private AsaasOrganizationRepository $asaasOrganizationRepository,
        private OrganizationRepository $organizationRepository,
    ) {}

    /**
     * Sync organization with existing ASAAS subaccount
     *
     * @param OrganizationDomain $organization
     * @param array $searchResults - Result from SearchSubaccount UseCase
     * @return OrganizationDomain
     * @throws AsaasException
     * @throws Throwable
     */
    public function perform(OrganizationDomain $organization, array $searchResults): OrganizationDomain
    {
        if ($organization->hasAsaasIntegration()) {
            throw new AsaasException("Organization already has an ASAAS integration");
        }

        if (empty($searchResults['data'])) {
            throw new AsaasException("No subaccounts found to sync with");
        }

        /** @var AsaasOrganization $asaasOrganization */
        $asaasOrganization = end($searchResults['data']);

        DB::beginTransaction();

        try {
            DBLog::logInfo('Starting ASAAS subaccount sync', 'SyncSubaccount', $organization->id, null, [
                'organization_id' => $organization->id,
                'asaas_account_id' => $asaasOrganization->asaas_account_id,
                'total_found_accounts' => count($searchResults['data'])
            ]);

            $storedAsaasOrganization = $this->asaasOrganizationRepository->store($asaasOrganization);
            $organization->setAsaasIntegration($storedAsaasOrganization);
            $this->organizationRepository->updateAsaasIntegration($organization);

            DB::commit();

            DBLog::logInfo('ASAAS subaccount sync completed successfully', 'SyncSubaccount',
                $organization->id, null, [
                'organization_id' => $organization->id,
                'asaas_account_id' => $organization->asaas_account_id,
                'asaas_organization_id' => $organization->asaas->id,
            ]);

            return $organization;

        } catch (Throwable $e) {
            DB::rollBack();

            DBLog::logError('Failed to sync ASAAS subaccount - unexpected error', 'SyncSubaccount',
                $organization->id, null, [
                'organization_id' => $organization->id,
                'asaas_account_id' => $organization->asaas?->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * Check if organization can sync with a subaccount
     *
     * @param OrganizationDomain $organization
     * @return bool
     */
    public function canSyncSubaccount(OrganizationDomain $organization): bool
    {
        // Already has integration
        if ($organization->hasAsaasIntegration()) {
            return false;
        }

        // Check required fields for matching
        if (empty($organization->id) || empty($organization->email)) {
            return false;
        }

        return true;
    }
}
