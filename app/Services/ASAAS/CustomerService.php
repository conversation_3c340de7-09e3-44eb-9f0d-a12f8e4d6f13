<?php

namespace App\Services\ASAAS;

use App\Domains\Organization;
use GuzzleHttp\Exception\GuzzleException;

/**
 * Customer Service
 *
 * Responsible for ASAAS customer endpoints.
 * Can be instantiated with both Main and Org credentials.
 */
class CustomerService extends AsaasService
{
    protected const ENDPOINT_CUSTOMERS = 'customers';

    public function __construct(?Organization $organization = null)
    {
        parent::__construct($organization);
        $this->endpoint = self::ENDPOINT_CUSTOMERS;
    }

    /**
     * Create a customer
     * @throws GuzzleException
     */
    public function create(array $data): array
    {
        $response = $this->post($data);
        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * Get customers list with filters
     * @throws GuzzleException
     */
    public function getAll(array $filters = []): array
    {
        $response = $this->get($filters);
        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * Get specific customer by ID
     * @throws GuzzleException
     */
    public function getById(string $customerId): array
    {
        $response = $this->get([], self::ENDPOINT_CUSTOMERS . "/{$customerId}");
        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * Update customer
     * @throws GuzzleException
     */
    public function update(string $customerId, array $data): array
    {
        $response = $this->put($data, self::ENDPOINT_CUSTOMERS . "/{$customerId}");
        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * Delete customer
     * @throws GuzzleException
     */
    public function deleteCustomer(string $customerId): array
    {
        $response = $this->delete(self::ENDPOINT_CUSTOMERS . "/{$customerId}");
        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * Search customers by email
     * @throws GuzzleException
     */
    public function searchByEmail(string $email): array
    {
        return $this->getAll(['email' => $email]);
    }

    /**
     * Search customers by CPF/CNPJ
     * @throws GuzzleException
     */
    public function searchByDocument(string $cpfCnpj): array
    {
        return $this->getAll(['cpfCnpj' => $cpfCnpj]);
    }

    /**
     * Get customer notifications
     * @throws GuzzleException
     */
    public function getNotifications(string $customerId, array $filters = []): array
    {
        $response = $this->get($filters, self::ENDPOINT_CUSTOMERS . "/{$customerId}/notifications");
        return json_decode($response->getBody()->getContents(), true);
    }
}
