<?php

namespace App\Services\ASAAS\Domains\Filters;

use App\Domains\Filters\Filters;
use App\Domains\Organization;

class SubaccountFilters extends Filters
{
    public const ALLOWED_FILTERS = [
        'externalId',
        'email',
    ];

    public function __construct(array $requestData)
    {
        parent::__construct(self::ALLOWED_FILTERS, $requestData);
    }

    /**
     * Get filters formatted for ASAAS API
     */
    public function toAsaasFilters(?Organization $organization = null): array
    {
        if ($organization === null) {
            return [];
        }
        return [
            'externalId' => $organization->id,
            'email' => $organization->email,
        ];
    }
}
