<?php

namespace App\Services\ASAAS\Examples;

use App\Models\Client;
use App\Models\Sale;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Services\ASAAS\UseCases\Deprecated\Clients\CreateCustomer;
use App\Services\ASAAS\UseCases\Deprecated\Sales\CreatePayment;
use App\Services\ASAAS\UseCases\Deprecated\Sales\SyncPaymentStatus;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

/**
 * Example class demonstrating how to use ASAAS Sales services
 *
 * This class provides examples of client management, payment creation,
 * and payment status synchronization using the ASAAS integration.
 */
class SalesUsageExample
{
    protected CreateCustomer $createCustomer;
    protected CreatePayment $createPayment;
    protected SyncPaymentStatus $syncPaymentStatus;

    public function __construct(
        CreateCustomer $createCustomer,
        CreatePayment $createPayment,
        SyncPaymentStatus $syncPaymentStatus
    ) {
        $this->createCustomer = $createCustomer;
        $this->createPayment = $createPayment;
        $this->syncPaymentStatus = $syncPaymentStatus;
    }

    /**
     * Example: Complete sale processing with ASAAS integration
     */
    public function processSaleWithAsaas(Sale $sale, array $paymentOptions = []): array
    {
        try {
            // Step 1: Ensure client has ASAAS customer
            $customerResult = $this->ensureClientHasAsaasCustomer($sale->client);

            if (!$customerResult['success']) {
                return [
                    'success' => false,
                    'message' => 'Failed to create ASAAS customer',
                    'error' => $customerResult
                ];
            }

            // Step 2: Create payment in ASAAS
            $paymentResult = $this->createPayment->perform($sale, $paymentOptions);

            if (!$paymentResult['success']) {
                return [
                    'success' => false,
                    'message' => 'Failed to create ASAAS payment',
                    'error' => $paymentResult
                ];
            }

            Log::info('Sale processed with ASAAS successfully', [
                'sale_id' => $sale->id,
                'asaas_payment_id' => $paymentResult['asaas_response']['id'],
                'client_id' => $sale->client_id,
                'value' => $sale->value
            ]);

            return [
                'success' => true,
                'message' => 'Sale processed with ASAAS successfully',
                'sale' => $paymentResult['sale'],
                'customer' => $customerResult['customer'] ?? null,
                'payment' => $paymentResult['asaas_response']
            ];

        } catch (AsaasException $e) {
            Log::error('Failed to process sale with ASAAS', [
                'sale_id' => $sale->id,
                'error' => $e->getMessage(),
                'asaas_error_code' => $e->getAsaasErrorCode()
            ]);

            return [
                'success' => false,
                'message' => 'ASAAS integration failed: ' . $e->getMessage(),
                'error_code' => $e->getAsaasErrorCode()
            ];
        }
    }

    /**
     * Example: Create boleto payment
     */
    public function createBoletoPayment(Sale $sale, array $options = []): array
    {
        $paymentOptions = array_merge([
            'billing_type' => 'BOLETO',
            'due_date' => Carbon::now()->addDays(7)->format('Y-m-d'),
            'description' => "Venda #{$sale->id} - {$sale->client->name}",
            'notifications' => [
                'enable_reminder' => true,
                'reminder_days' => 3
            ]
        ], $options);

        return $this->createPayment->perform($sale, $paymentOptions);
    }

    /**
     * Example: Create PIX payment
     */
    public function createPixPayment(Sale $sale, array $options = []): array
    {
        $paymentOptions = array_merge([
            'billing_type' => 'PIX',
            'due_date' => Carbon::now()->addDays(1)->format('Y-m-d'),
            'description' => "Venda #{$sale->id} - PIX - {$sale->client->name}",
        ], $options);

        return $this->createPayment->createPixPayment($sale, $paymentOptions);
    }

    /**
     * Example: Create credit card payment
     */
    public function createCreditCardPayment(Sale $sale, array $creditCardData, array $options = []): array
    {
        $paymentOptions = array_merge([
            'due_date' => Carbon::now()->format('Y-m-d'),
            'description' => "Venda #{$sale->id} - Cartão - {$sale->client->name}",
        ], $options);

        return $this->createPayment->createWithCreditCard($sale, $creditCardData, $paymentOptions);
    }

    /**
     * Example: Create installment payment
     */
    public function createInstallmentPayment(Sale $sale, int $installments, array $options = []): array
    {
        if ($installments < 2 || $installments > 12) {
            return [
                'success' => false,
                'message' => 'Installments must be between 2 and 12'
            ];
        }

        $paymentOptions = array_merge([
            'billing_type' => 'CREDIT_CARD',
            'due_date' => Carbon::now()->format('Y-m-d'),
            'description' => "Venda #{$sale->id} - {$installments}x - {$sale->client->name}",
        ], $options);

        return $this->createPayment->createInstallment($sale, $installments, $paymentOptions);
    }

    /**
     * Example: Create payment with discount
     */
    public function createPaymentWithDiscount(Sale $sale, float $discountValue, int $discountDays = 5, array $options = []): array
    {
        $paymentOptions = array_merge([
            'billing_type' => 'BOLETO',
            'due_date' => Carbon::now()->addDays(7)->format('Y-m-d'),
            'description' => "Venda #{$sale->id} - Desconto - {$sale->client->name}",
            'discount' => [
                'value' => $discountValue,
                'due_date_limit_days' => $discountDays
            ]
        ], $options);

        return $this->createPayment->perform($sale, $paymentOptions);
    }

    /**
     * Example: Sync payment status and handle different scenarios
     */
    public function syncAndHandlePaymentStatus(Sale $sale): array
    {
        $syncResult = $this->syncPaymentStatus->perform($sale);

        if (!$syncResult['success']) {
            return [
                'success' => false,
                'message' => 'Failed to sync payment status',
                'error' => $syncResult
            ];
        }

        $sale = $syncResult['sale'];
        $response = [
            'success' => true,
            'message' => 'Payment status synced successfully',
            'sale' => $sale,
            'status' => $sale->payment_status->value,
            'actions' => []
        ];

        // Add specific actions based on payment status
        if ($sale->isPaid()) {
            $response['actions'][] = [
                'action' => 'payment_confirmed',
                'message' => 'Payment confirmed - process order fulfillment',
                'priority' => 'high'
            ];
        } elseif ($sale->isOverdue()) {
            $response['actions'][] = [
                'action' => 'payment_overdue',
                'message' => 'Payment is overdue - send reminder or collection notice',
                'priority' => 'high'
            ];
        } elseif ($sale->isPending()) {
            $response['actions'][] = [
                'action' => 'payment_pending',
                'message' => 'Payment is pending - monitor status',
                'priority' => 'medium'
            ];
        }

        return $response;
    }

    /**
     * Example: Bulk sync payments for organization
     */
    public function bulkSyncOrganizationPayments(int $organizationId, int $limit = 50): array
    {
        try {
            $result = $this->syncPaymentStatus->syncForOrganization($organizationId, null, $limit);

            Log::info('Bulk payment sync completed', [
                'organization_id' => $organizationId,
                'total_synced' => $result['total_synced'],
                'total_errors' => $result['total_errors']
            ]);

            return [
                'success' => true,
                'message' => "Synced {$result['total_synced']} payments with {$result['total_errors']} errors",
                'results' => $result['results'],
                'errors' => $result['errors'],
                'summary' => [
                    'total_synced' => $result['total_synced'],
                    'total_errors' => $result['total_errors'],
                    'success_rate' => $result['total_synced'] > 0 ?
                        round(($result['total_synced'] / ($result['total_synced'] + $result['total_errors'])) * 100, 2) : 0
                ]
            ];

        } catch (\Exception $e) {
            Log::error('Failed to bulk sync payments', [
                'organization_id' => $organizationId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to bulk sync payments: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Example: Create customers for all clients without ASAAS integration
     */
    public function bulkCreateCustomers(int $organizationId): array
    {
        try {
            $result = $this->createCustomer->createForOrganization($organizationId);

            Log::info('Bulk customer creation completed', [
                'organization_id' => $organizationId,
                'total_processed' => $result['total_processed'],
                'total_errors' => $result['total_errors']
            ]);

            return [
                'success' => true,
                'message' => "Created {$result['total_processed']} customers with {$result['total_errors']} errors",
                'results' => $result['results'],
                'errors' => $result['errors'],
                'summary' => [
                    'total_processed' => $result['total_processed'],
                    'total_errors' => $result['total_errors'],
                    'success_rate' => $result['total_processed'] > 0 ?
                        round(($result['total_processed'] / ($result['total_processed'] + $result['total_errors'])) * 100, 2) : 0
                ]
            ];

        } catch (\Exception $e) {
            Log::error('Failed to bulk create customers', [
                'organization_id' => $organizationId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to bulk create customers: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Example: Get payment dashboard data
     */
    public function getPaymentDashboard(int $organizationId): array
    {
        $syncSummary = $this->syncPaymentStatus->getSyncSummary($organizationId);

        // Get payment status distribution
        $statusDistribution = Sale::where('organization_id', $organizationId)
            ->whereNotNull('asaas_payment_id')
            ->selectRaw('payment_status, COUNT(*) as count')
            ->groupBy('payment_status')
            ->pluck('count', 'payment_status')
            ->toArray();

        // Get recent payments
        $recentPayments = Sale::where('organization_id', $organizationId)
            ->whereNotNull('asaas_payment_id')
            ->with('client')
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get()
            ->map(fn($sale) => $sale->getPaymentSummary());

        // Get overdue payments
        $overduePayments = Sale::where('organization_id', $organizationId)
            ->where('payment_status', 'overdue')
            ->with('client')
            ->orderBy('due_date', 'asc')
            ->limit(10)
            ->get()
            ->map(fn($sale) => $sale->getPaymentSummary());

        return [
            'organization_id' => $organizationId,
            'sync_summary' => $syncSummary,
            'status_distribution' => $statusDistribution,
            'recent_payments' => $recentPayments,
            'overdue_payments' => $overduePayments,
            'alerts' => $this->getPaymentAlerts($organizationId, $syncSummary, $statusDistribution),
            'quick_actions' => $this->getQuickActions($organizationId, $syncSummary)
        ];
    }

    /**
     * Ensure client has ASAAS customer
     */
    protected function ensureClientHasAsaasCustomer(Client $client): array
    {
        if ($client->hasAsaasIntegration()) {
            return [
                'success' => true,
                'message' => 'Client already has ASAAS customer',
                'customer' => null
            ];
        }

        if (!$this->createCustomer->canCreateCustomer($client)) {
            return [
                'success' => false,
                'message' => 'Client cannot create ASAAS customer - missing required data',
                'required_fields' => ['name', 'email', 'document']
            ];
        }

        return $this->createCustomer->perform($client);
    }

    /**
     * Get payment alerts
     */
    protected function getPaymentAlerts(int $organizationId, array $syncSummary, array $statusDistribution): array
    {
        $alerts = [];

        // Sync alerts
        if ($syncSummary['needs_sync'] > 0) {
            $alerts[] = [
                'type' => 'warning',
                'message' => "{$syncSummary['needs_sync']} payments need synchronization",
                'action' => 'sync_payments'
            ];
        }

        if ($syncSummary['with_errors'] > 0) {
            $alerts[] = [
                'type' => 'error',
                'message' => "{$syncSummary['with_errors']} payments have sync errors",
                'action' => 'check_sync_errors'
            ];
        }

        // Overdue alerts
        $overdueCount = $statusDistribution['overdue'] ?? 0;
        if ($overdueCount > 0) {
            $alerts[] = [
                'type' => 'danger',
                'message' => "{$overdueCount} overdue payments require attention",
                'action' => 'handle_overdue_payments'
            ];
        }

        return $alerts;
    }

    /**
     * Get quick actions
     */
    protected function getQuickActions(int $organizationId, array $syncSummary): array
    {
        $actions = [];

        if ($syncSummary['needs_sync'] > 0) {
            $actions[] = [
                'action' => 'sync_payments',
                'title' => 'Sync Payment Status',
                'description' => "Sync {$syncSummary['needs_sync']} payments",
                'priority' => 'high'
            ];
        }

        $actions[] = [
            'action' => 'create_customers',
            'title' => 'Create Missing Customers',
            'description' => 'Create ASAAS customers for clients without integration',
            'priority' => 'medium'
        ];

        return $actions;
    }
}
