<?php

namespace App\Services\ASAAS\Examples;

use App\Domains\Organization\Organization as OrganizationDomain;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Services\ASAAS\UseCases\Deprecated\Organizations\CheckSubscriptionStatus;
use App\Services\ASAAS\UseCases\Deprecated\Organizations\CreateSubaccount;
use App\Services\ASAAS\UseCases\Deprecated\Organizations\CreateSystemSubscription;
use App\Services\ASAAS\UseCases\Deprecated\Organizations\GetBillingDetails;
use App\Services\ASAAS\UseCases\Deprecated\Organizations\IsAllowedToUseSystem;
use Illuminate\Support\Facades\Log;

/**
 * Example class demonstrating how to use ASAAS Organization services
 *
 * This class provides examples of organization management, subscription handling,
 * and access control using the ASAAS integration.
 */
class OrganizationUsageExample
{
    protected CreateSubaccount $createSubaccount;
    protected CreateSystemSubscription $createSubscription;
    protected GetBillingDetails $getBillingDetails;
    protected IsAllowedToUseSystem $isAllowedToUseSystem;
    protected CheckSubscriptionStatus $checkSubscriptionStatus;

    public function __construct(
        CreateSubaccount $createSubaccount,
        CreateSystemSubscription $createSubscription,
        GetBillingDetails $getBillingDetails,
        IsAllowedToUseSystem $isAllowedToUseSystem,
        CheckSubscriptionStatus $checkSubscriptionStatus
    ) {
        $this->createSubaccount = $createSubaccount;
        $this->createSubscription = $createSubscription;
        $this->getBillingDetails = $getBillingDetails;
        $this->isAllowedToUseSystem = $isAllowedToUseSystem;
        $this->checkSubscriptionStatus = $checkSubscriptionStatus;
    }

    /**
     * Example: Complete organization setup with ASAAS integration
     */
    public function setupOrganizationWithAsaas(OrganizationDomain $organization): array
    {
        try {
            // Step 1: Check if organization can create subaccount
            if (!$this->createSubaccount->canCreateSubaccount($organization)) {
                return [
                    'success' => false,
                    'message' => 'Organization cannot create ASAAS subaccount - missing required data',
                    'required_fields' => ['name', 'email', 'document', 'postal_code', 'phone']
                ];
            }

            // Step 2: Create ASAAS subaccount
            $subaccountResult = $this->createSubaccount->perform($organization);

            if (!$subaccountResult['success']) {
                return [
                    'success' => false,
                    'message' => 'Failed to create ASAAS subaccount',
                    'error' => $subaccountResult
                ];
            }

            Log::info('Organization ASAAS setup completed', [
                'organization_id' => $organization->id,
                'asaas_account_id' => $subaccountResult['asaas_response']['id']
            ]);

            return [
                'success' => true,
                'message' => 'Organization setup with ASAAS completed successfully',
                'organization' => $subaccountResult['organization'],
                'asaas_account_id' => $subaccountResult['asaas_response']['id']
            ];

        } catch (AsaasException $e) {
            Log::error('Failed to setup organization with ASAAS', [
                'organization_id' => $organization->id,
                'error' => $e->getMessage(),
                'asaas_error_code' => $e->getAsaasErrorCode()
            ]);

            return [
                'success' => false,
                'message' => 'ASAAS integration failed: ' . $e->getMessage(),
                'error_code' => $e->getAsaasErrorCode()
            ];
        }
    }

    /**
     * Example: Create subscription for organization
     */
    public function createOrganizationSubscription(
        OrganizationDomain $organization,
        string $planType = 'basic',
        string $billingType = 'BOLETO'
    ): array {
        try {
            // Get available plans
            $plans = $this->createSubscription->getAvailablePlans();

            if (!isset($plans[$planType])) {
                return [
                    'success' => false,
                    'message' => 'Invalid plan type',
                    'available_plans' => array_keys($plans)
                ];
            }

            $plan = $plans[$planType];

            // Create subscription
            $subscriptionResult = $this->createSubscription->perform(
                $organization,
                $plan['value'],
                $billingType,
                [
                    'description' => $plan['name'] . ' - ' . $organization->name,
                    'cycle' => 'MONTHLY'
                ]
            );

            Log::info('Organization subscription created', [
                'organization_id' => $organization->id,
                'plan' => $planType,
                'value' => $plan['value'],
                'billing_type' => $billingType
            ]);

            return $subscriptionResult;

        } catch (AsaasException $e) {
            Log::error('Failed to create organization subscription', [
                'organization_id' => $organization->id,
                'plan' => $planType,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to create subscription: ' . $e->getMessage(),
                'error_code' => $e->getAsaasErrorCode()
            ];
        }
    }

    /**
     * Example: Check organization access and handle different scenarios
     */
    public function checkOrganizationAccess(OrganizationDomain $organization): array
    {
        $accessResult = $this->isAllowedToUseSystem->perform($organization);

        $response = [
            'organization_id' => $organization->id,
            'organization_name' => $organization->name,
            'access_allowed' => $accessResult['allowed'],
            'access_reason' => $accessResult['reason'],
            'message' => $accessResult['message'],
            'recommendations' => []
        ];

        // Add specific recommendations based on access status
        if (!$accessResult['allowed']) {
            switch ($accessResult['reason']) {
                case 'organization_inactive':
                    $response['recommendations'][] = 'Activate the organization';
                    break;

                case 'organization_suspended':
                    $response['recommendations'][] = 'Contact support to unsuspend the organization';
                    break;

                case 'no_asaas_integration':
                    $response['recommendations'][] = 'Create ASAAS subaccount integration';
                    $response['can_create_subaccount'] = $this->createSubaccount->canCreateSubaccount($organization);
                    break;

                case 'subscription_inactive':
                    $response['recommendations'][] = 'Create a subscription plan';
                    $response['available_plans'] = $this->createSubscription->getAvailablePlans();
                    break;

                case 'subscription_overdue':
                case 'subscription_overdue_expired':
                    $response['recommendations'][] = 'Pay overdue subscription';
                    break;

                case 'courtesy_expired':
                    $response['recommendations'][] = 'Create subscription or extend courtesy period';
                    break;
            }
        }

        return $response;
    }

    /**
     * Example: Get complete billing information
     */
    public function getOrganizationBilling(OrganizationDomain $organization): array
    {
        try {
            $billingDetails = $this->getBillingDetails->perform($organization);

            // Add summary information
            $billingDetails['summary'] = [
                'has_asaas_integration' => $billingDetails['asaas_integration']['has_integration'],
                'has_active_subscription' => $billingDetails['subscription']['has_subscription'] &&
                                           $billingDetails['subscription']['status'] === 'active',
                'is_courtesy_access' => $billingDetails['courtesy']['is_courtesy'],
                'access_allowed' => $billingDetails['access_status']['allowed'],
                'pending_actions_count' => count($billingDetails['next_actions']),
                'billing_history_count' => count($billingDetails['billing_history'])
            ];

            return $billingDetails;

        } catch (\Exception $e) {
            Log::error('Failed to get organization billing details', [
                'organization_id' => $organization->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to get billing details: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Example: Update subscription status from ASAAS
     */
    public function syncSubscriptionStatus(OrganizationDomain $organization): array
    {
        try {
            $statusResult = $this->checkSubscriptionStatus->perform($organization);

            Log::info('Subscription status synced', [
                'organization_id' => $organization->id,
                'success' => $statusResult['success'],
                'status' => $statusResult['status'] ?? 'unknown'
            ]);

            return $statusResult;

        } catch (\Exception $e) {
            Log::error('Failed to sync subscription status', [
                'organization_id' => $organization->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to sync status: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Example: Grant courtesy access to organization
     */
    public function grantCourtesyAccess(
        OrganizationDomain $organization,
        int $days = 30,
        string $reason = 'Courtesy access granted'
    ): array {
        try {
            $expiresAt = now()->addDays($days);

            $organization->update([
                'is_courtesy' => true,
                'courtesy_expires_at' => $expiresAt->toDateString(),
                'courtesy_reason' => $reason
            ]);

            Log::info('Courtesy access granted', [
                'organization_id' => $organization->id,
                'expires_at' => $expiresAt->toDateString(),
                'days' => $days,
                'reason' => $reason
            ]);

            return [
                'success' => true,
                'message' => "Courtesy access granted for {$days} days",
                'expires_at' => $expiresAt->toDateString(),
                'reason' => $reason
            ];

        } catch (\Exception $e) {
            Log::error('Failed to grant courtesy access', [
                'organization_id' => $organization->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to grant courtesy access: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Example: Get organization dashboard data
     */
    public function getOrganizationDashboard(OrganizationDomain $organization): array
    {
        $accessStatus = $this->isAllowedToUseSystem->perform($organization);
        $billingDetails = $this->getBillingDetails->getBillingSummary($organization);

        return [
            'organization' => [
                'id' => $organization->id,
                'name' => $organization->name,
                'is_active' => $organization->is_active,
                'is_suspended' => $organization->is_suspended,
                'user_count' => $organization->users()->count()
            ],
            'access' => [
                'allowed' => $accessStatus['allowed'],
                'reason' => $accessStatus['reason'],
                'message' => $accessStatus['message']
            ],
            'billing' => $billingDetails,
            'quick_actions' => $this->getQuickActions($organization, $accessStatus),
            'alerts' => $this->getAlerts($organization, $accessStatus, $billingDetails)
        ];
    }

    /**
     * Get quick actions for organization
     */
    protected function getQuickActions(OrganizationDomain $organization, array $accessStatus): array
    {
        $actions = [];

        if (!$accessStatus['allowed']) {
            switch ($accessStatus['reason']) {
                case 'no_asaas_integration':
                    if ($this->createSubaccount->canCreateSubaccount($organization)) {
                        $actions[] = [
                            'action' => 'create_asaas_subaccount',
                            'title' => 'Setup ASAAS Integration',
                            'priority' => 'high'
                        ];
                    }
                    break;

                case 'subscription_inactive':
                    $actions[] = [
                        'action' => 'create_subscription',
                        'title' => 'Create Subscription',
                        'priority' => 'high'
                    ];
                    break;
            }
        }

        return $actions;
    }

    /**
     * Get alerts for organization
     */
    protected function getAlerts(OrganizationDomain $organization, array $accessStatus, array $billingDetails): array
    {
        $alerts = [];

        if (!$accessStatus['allowed']) {
            $alerts[] = [
                'type' => 'error',
                'message' => $accessStatus['message'],
                'action_required' => true
            ];
        }

        if ($billingDetails['has_pending_actions']) {
            $alerts[] = [
                'type' => 'warning',
                'message' => 'Billing actions required',
                'action_required' => true
            ];
        }

        return $alerts;
    }
}
