<?php

namespace App\Services\ASAAS;

use App\Domains\Organization;
use GuzzleHttp\Exception\GuzzleException;

/**
 * Payment Service
 *
 * Responsible for ASAAS payments endpoints.
 * Always uses organization credentials (organizations pay us in subscriptions).
 */
class PaymentService extends AsaasService
{
    protected const ENDPOINT_PAYMENTS = 'payments';

    public function __construct(Organization $organization)
    {
        parent::__construct($organization); // Organization credentials
        $this->endpoint = self::ENDPOINT_PAYMENTS;
    }

    /**
     * Create a payment
     * @throws GuzzleException
     */
    public function create(array $data): array
    {
        $response = $this->post($data);
        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * Get payments list with filters
     * @throws GuzzleException
     */
    public function getAll(array $filters = []): array
    {
        $response = $this->get($filters);
        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * Get specific payment by ID
     * @throws GuzzleException
     */
    public function getById(string $paymentId): array
    {
        $response = $this->get([], self::ENDPOINT_PAYMENTS . "/{$paymentId}");
        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * Update payment
     * @throws GuzzleException
     */
    public function update(string $paymentId, array $data): array
    {
        $response = $this->put($data, self::ENDPOINT_PAYMENTS . "/{$paymentId}");
        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * Delete payment
     * @throws GuzzleException
     */
    public function delete(string $paymentId): array
    {
        $response = $this->delete(self::ENDPOINT_PAYMENTS . "/{$paymentId}");
        return json_decode($response->getBody()->getContents(), true);
    }
}
