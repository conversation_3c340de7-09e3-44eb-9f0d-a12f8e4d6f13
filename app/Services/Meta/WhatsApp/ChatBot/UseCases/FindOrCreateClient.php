<?php

namespace App\Services\Meta\WhatsApp\ChatBot\UseCases;

use App\Domains\Inventory\Client;
use App\Factories\Inventory\ClientFactory;
use App\Repositories\ClientRepository;
use Exception;

class FindOrCreateClient
{
    protected ClientRepository $clientRepository;
    protected ClientFactory $clientFactory;

    public function __construct(
        ClientRepository $clientRepository,
        ClientFactory $clientFactory
    ) {
        $this->clientRepository = $clientRepository;
        $this->clientFactory = $clientFactory;
    }

    /**
     * Find existing client or create new one from WhatsApp message data
     *
     * @param array $messageData
     * @return Client
     * @throws Exception
     */
    public function perform(array $messageData): Client
    {
        $phoneNumber = $messageData['from'];
        $organizationId = $this->getOrganizationIdFromPhoneNumber($messageData);

        // Try to find existing client by phone number
        $existingClient = $this->findClientByPhone($phoneNumber, $organizationId);
        
        if ($existingClient) {
            // Update client name if we have better information from What<PERSON><PERSON><PERSON>
            return $this->updateClientIfNeeded($existingClient, $messageData);
        }

        // Create new client
        return $this->createNewClient($messageData, $organizationId);
    }

    /**
     * Find client by phone number within organization
     */
    protected function findClientByPhone(string $phoneNumber, int $organizationId): ?Client
    {
        // Try exact match first
        $client = $this->clientRepository->findByPhoneAndOrganization($phoneNumber, $organizationId);
        
        if ($client) {
            return $client;
        }

        // Try with different phone number formats
        $phoneVariations = $this->generatePhoneVariations($phoneNumber);
        
        foreach ($phoneVariations as $variation) {
            $client = $this->clientRepository->findByPhoneAndOrganization($variation, $organizationId);
            if ($client) {
                return $client;
            }
        }

        return null;
    }

    /**
     * Update client information if we have better data from WhatsApp
     */
    protected function updateClientIfNeeded(Client $client, array $messageData): Client
    {
        $needsUpdate = false;
        
        // Update name if client doesn't have one or WhatsApp provides a better one
        $whatsappName = $messageData['contact_name'] ?? $messageData['profile_name'];
        if ($whatsappName && (empty($client->name) || $client->name === $client->phone)) {
            $client->name = $whatsappName;
            $needsUpdate = true;
        }

        if ($needsUpdate) {
            return $this->clientRepository->save($client);
        }

        return $client;
    }

    /**
     * Create new client from WhatsApp message data
     */
    protected function createNewClient(array $messageData, int $organizationId): Client
    {
        $phoneNumber = $messageData['from'];
        $name = $messageData['contact_name'] ?? 
                $messageData['profile_name'] ?? 
                "WhatsApp User {$phoneNumber}";

        // Create client domain object
        $client = new Client(
            null, // id
            $organizationId,
            $name,
            $phoneNumber, // phone
            null, // email
            null, // profession
            null, // birthdate
            null, // cpf
            null, // cnpj
            null, // service
            null, // address
            null, // number
            null, // neighborhood
            null, // cep
            null, // complement
            null, // civil_state
            "Created from WhatsApp: {$messageData['message_id']}" // description
        );

        return $this->clientRepository->store($client);
    }

    /**
     * Get organization ID from phone number metadata
     */
    protected function getOrganizationIdFromPhoneNumber(array $messageData): int
    {
        $phoneNumberId = $messageData['phone_number_id'];
        
        if (!$phoneNumberId) {
            throw new Exception('Cannot determine organization: missing phone_number_id in webhook');
        }

        // TODO: Implement logic to get organization from phone_number_id
        // For now, we'll use a default organization ID
        // This should be replaced with actual lookup logic
        return 1; // Default organization ID
    }

    /**
     * Generate phone number variations for matching
     */
    protected function generatePhoneVariations(string $phoneNumber): array
    {
        $variations = [];
        
        // Remove all non-digits
        $digitsOnly = preg_replace('/[^0-9]/', '', $phoneNumber);
        $variations[] = $digitsOnly;
        
        // Add common formatting variations
        if (strlen($digitsOnly) >= 10) {
            // Add with parentheses and dash: (11) 99999-9999
            $formatted = '(' . substr($digitsOnly, 0, 2) . ') ' . 
                        substr($digitsOnly, 2, 5) . '-' . 
                        substr($digitsOnly, 7);
            $variations[] = $formatted;
            
            // Add with spaces: 11 99999 9999
            $spaced = substr($digitsOnly, 0, 2) . ' ' . 
                     substr($digitsOnly, 2, 5) . ' ' . 
                     substr($digitsOnly, 7);
            $variations[] = $spaced;
        }
        
        return array_unique($variations);
    }
}
