<?php

namespace App\Console\Commands\ASAAS;

use App\Models\Organization;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Services\ASAAS\UseCases\Deprecated\Organizations\CheckSubscriptionStatus;
use App\Services\ASAAS\UseCases\Deprecated\Organizations\CreateSubaccount;
use App\Services\ASAAS\UseCases\Deprecated\Organizations\CreateSystemSubscription;
use App\Services\ASAAS\UseCases\Deprecated\Organizations\IsAllowedToUseSystem;
use Illuminate\Console\Command;

class ManageOrganizations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'asaas:organizations
                            {action : Action to perform (setup, check-access, sync-status, grant-courtesy, list)}
                            {--organization= : Organization ID to target}
                            {--all : Apply to all organizations}
                            {--days=30 : Days for courtesy access}
                            {--reason= : Reason for courtesy access}
                            {--plan=basic : Subscription plan type}
                            {--billing-type=BOLETO : Billing type for subscription}
                            {--dry-run : Show what would be done without executing}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manage ASAAS organizations - setup, check access, sync status, grant courtesy';

    protected CreateSubaccount $createSubaccount;
    protected CreateSystemSubscription $createSubscription;
    protected CheckSubscriptionStatus $checkStatus;
    protected IsAllowedToUseSystem $isAllowed;

    public function __construct(
        CreateSubaccount $createSubaccount,
        CreateSystemSubscription $createSubscription,
        CheckSubscriptionStatus $checkStatus,
        IsAllowedToUseSystem $isAllowed
    ) {
        parent::__construct();
        $this->createSubaccount = $createSubaccount;
        $this->createSubscription = $createSubscription;
        $this->checkStatus = $checkStatus;
        $this->isAllowed = $isAllowed;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $action = $this->argument('action');
        $isDryRun = $this->option('dry-run');

        if ($isDryRun) {
            $this->info('🔍 DRY RUN MODE - No changes will be made');
            $this->newLine();
        }

        switch ($action) {
            case 'setup':
                return $this->setupOrganizations($isDryRun);

            case 'check-access':
                return $this->checkOrganizationAccess();

            case 'sync-status':
                return $this->syncSubscriptionStatus($isDryRun);

            case 'grant-courtesy':
                return $this->grantCourtesyAccess($isDryRun);

            case 'list':
                return $this->listOrganizations();

            default:
                $this->error("Unknown action: {$action}");
                $this->info('Available actions: setup, check-access, sync-status, grant-courtesy, list');
                return 1;
        }
    }

    /**
     * Setup ASAAS integration for organizations
     */
    protected function setupOrganizations(bool $isDryRun): int
    {
        $organizations = $this->getTargetOrganizations();

        if ($organizations->isEmpty()) {
            $this->warn('No organizations found to setup');
            return 0;
        }

        $this->info("Setting up ASAAS integration for {$organizations->count()} organization(s)");
        $this->newLine();

        $successCount = 0;
        $errorCount = 0;

        foreach ($organizations as $organization) {
            $this->info("Processing: {$organization->name} (ID: {$organization->id})");

            // Check if can create subaccount
            if (!$this->createSubaccount->canCreateSubaccount($organization)) {
                $this->warn("  ⚠️  Cannot create subaccount - missing required data");
                $errorCount++;
                continue;
            }

            if ($organization->asaas_account_id) {
                $this->info("  ✅ Already has ASAAS integration");
                $successCount++;
                continue;
            }

            if ($isDryRun) {
                $this->info("  🔍 Would create ASAAS subaccount");
                $successCount++;
                continue;
            }

            try {
                $result = $this->createSubaccount->perform($organization);

                if ($result['success']) {
                    $this->info("  ✅ ASAAS subaccount created: {$result['asaas_response']['id']}");
                    $successCount++;
                } else {
                    $this->error("  ❌ Failed to create subaccount");
                    $errorCount++;
                }

            } catch (AsaasException $e) {
                $this->error("  ❌ ASAAS Error: {$e->getMessage()}");
                $errorCount++;
            } catch (\Exception $e) {
                $this->error("  ❌ Error: {$e->getMessage()}");
                $errorCount++;
            }
        }

        $this->newLine();
        $this->info("Setup completed: {$successCount} successful, {$errorCount} errors");

        return $errorCount > 0 ? 1 : 0;
    }

    /**
     * Check organization access status
     */
    protected function checkOrganizationAccess(): int
    {
        $organizations = $this->getTargetOrganizations();

        if ($organizations->isEmpty()) {
            $this->warn('No organizations found to check');
            return 0;
        }

        $this->info("Checking access for {$organizations->count()} organization(s)");
        $this->newLine();

        $allowedCount = 0;
        $deniedCount = 0;

        foreach ($organizations as $organization) {
            $accessResult = $this->isAllowed->perform($organization);

            $status = $accessResult['allowed'] ? '✅ ALLOWED' : '❌ DENIED';
            $this->info("{$organization->name} (ID: {$organization->id}): {$status}");
            $this->info("  Reason: {$accessResult['reason']}");
            $this->info("  Message: {$accessResult['message']}");

            if ($accessResult['allowed']) {
                $allowedCount++;
            } else {
                $deniedCount++;
            }

            $this->newLine();
        }

        $this->info("Access check completed: {$allowedCount} allowed, {$deniedCount} denied");

        return 0;
    }

    /**
     * Sync subscription status from ASAAS
     */
    protected function syncSubscriptionStatus(bool $isDryRun): int
    {
        $organizations = $this->getTargetOrganizations()
            ->filter(fn($org) => !empty($org->asaas_account_id));

        if ($organizations->isEmpty()) {
            $this->warn('No organizations with ASAAS integration found');
            return 0;
        }

        $this->info("Syncing subscription status for {$organizations->count()} organization(s)");
        $this->newLine();

        $successCount = 0;
        $errorCount = 0;

        foreach ($organizations as $organization) {
            $this->info("Syncing: {$organization->name} (ID: {$organization->id})");

            if ($isDryRun) {
                $this->info("  🔍 Would sync subscription status");
                $successCount++;
                continue;
            }

            try {
                $result = $this->checkStatus->perform($organization);

                if ($result['success']) {
                    $this->info("  ✅ Status synced: {$result['status']}");
                    $successCount++;
                } else {
                    $this->warn("  ⚠️  {$result['message']}");
                    $errorCount++;
                }

            } catch (\Exception $e) {
                $this->error("  ❌ Error: {$e->getMessage()}");
                $errorCount++;
            }
        }

        $this->newLine();
        $this->info("Sync completed: {$successCount} successful, {$errorCount} errors");

        return $errorCount > 0 ? 1 : 0;
    }

    /**
     * Grant courtesy access to organizations
     */
    protected function grantCourtesyAccess(bool $isDryRun): int
    {
        $organizations = $this->getTargetOrganizations();
        $days = (int) $this->option('days');
        $reason = $this->option('reason') ?: 'Courtesy access granted via command';

        if ($organizations->isEmpty()) {
            $this->warn('No organizations found');
            return 0;
        }

        $this->info("Granting {$days} days courtesy access to {$organizations->count()} organization(s)");
        $this->info("Reason: {$reason}");
        $this->newLine();

        $successCount = 0;
        $errorCount = 0;

        foreach ($organizations as $organization) {
            $this->info("Processing: {$organization->name} (ID: {$organization->id})");

            if ($isDryRun) {
                $this->info("  🔍 Would grant {$days} days courtesy access");
                $successCount++;
                continue;
            }

            try {
                $expiresAt = now()->addDays($days);

                $organization->update([
                    'is_courtesy' => true,
                    'courtesy_expires_at' => $expiresAt->toDateString(),
                    'courtesy_reason' => $reason
                ]);

                $this->info("  ✅ Courtesy access granted until {$expiresAt->format('Y-m-d')}");
                $successCount++;

            } catch (\Exception $e) {
                $this->error("  ❌ Error: {$e->getMessage()}");
                $errorCount++;
            }
        }

        $this->newLine();
        $this->info("Courtesy access granted: {$successCount} successful, {$errorCount} errors");

        return $errorCount > 0 ? 1 : 0;
    }

    /**
     * List organizations with their status
     */
    protected function listOrganizations(): int
    {
        $organizations = $this->getTargetOrganizations();

        if ($organizations->isEmpty()) {
            $this->warn('No organizations found');
            return 0;
        }

        $this->info("Organization Status Report");
        $this->newLine();

        $headers = ['ID', 'Name', 'Active', 'ASAAS', 'Subscription', 'Courtesy', 'Access'];
        $rows = [];

        foreach ($organizations as $organization) {
            $accessResult = $this->isAllowed->perform($organization);

            $rows[] = [
                $organization->id,
                $organization->name,
                $organization->is_active ? '✅' : '❌',
                $organization->asaas_account_id ? '✅' : '❌',
                $organization->subscription_status ?: 'none',
                $organization->is_courtesy ? '✅' : '❌',
                $accessResult['allowed'] ? '✅' : '❌'
            ];
        }

        $this->table($headers, $rows);

        return 0;
    }

    /**
     * Get target organizations based on options
     */
    protected function getTargetOrganizations()
    {
        if ($this->option('all')) {
            return Organization::all();
        }

        if ($organizationId = $this->option('organization')) {
            $organization = Organization::find($organizationId);

            if (!$organization) {
                $this->error("Organization with ID {$organizationId} not found");
                return collect();
            }

            return collect([$organization]);
        }

        $this->error('Please specify --organization=ID or --all');
        return collect();
    }
}
