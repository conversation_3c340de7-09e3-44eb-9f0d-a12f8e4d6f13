<?php

namespace App\Console\Commands\ASAAS;

use App\Models\Client;
use App\Models\Organization;
use App\Models\Sale;
use App\Services\ASAAS\UseCases\Deprecated\Clients\CreateCustomer;
use App\Services\ASAAS\UseCases\Deprecated\Sales\SyncPaymentStatus;
use Illuminate\Console\Command;

class SyncPaymentsStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'asaas:sync-payments
                            {action : Action to perform (sync-status, create-customers, sync-overdue)}
                            {--organization= : Organization ID to target}
                            {--sale= : Sale ID to target}
                            {--client= : Client ID to target}
                            {--all : Apply to all organizations}
                            {--limit=100 : Limit number of records to process}
                            {--dry-run : Show what would be done without executing}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync ASAAS payments status and create customers';

    protected SyncPaymentStatus $syncPaymentStatus;
    protected CreateCustomer $createCustomer;

    public function __construct(
        SyncPaymentStatus $syncPaymentStatus,
        CreateCustomer $createCustomer
    ) {
        parent::__construct();
        $this->syncPaymentStatus = $syncPaymentStatus;
        $this->createCustomer = $createCustomer;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $action = $this->argument('action');
        $isDryRun = $this->option('dry-run');

        if ($isDryRun) {
            $this->info('🔍 DRY RUN MODE - No changes will be made');
            $this->newLine();
        }

        switch ($action) {
            case 'sync-status':
                return $this->syncPaymentStatus($isDryRun);

            case 'create-customers':
                return $this->createCustomers($isDryRun);

            case 'sync-overdue':
                return $this->syncOverduePayments($isDryRun);

            default:
                $this->error("Unknown action: {$action}");
                $this->info('Available actions: sync-status, create-customers, sync-overdue');
                return 1;
        }
    }

    /**
     * Sync payment status
     */
    protected function syncPaymentStatus(bool $isDryRun): int
    {
        if ($saleId = $this->option('sale')) {
            return $this->syncSingleSale($saleId, $isDryRun);
        }

        if ($organizationId = $this->option('organization')) {
            return $this->syncOrganizationPayments($organizationId, $isDryRun);
        }

        if ($this->option('all')) {
            return $this->syncAllPayments($isDryRun);
        }

        $this->error('Please specify --sale=ID, --organization=ID, or --all');
        return 1;
    }

    /**
     * Sync single sale payment status
     */
    protected function syncSingleSale(int $saleId, bool $isDryRun): int
    {
        try {
            $sale = Sale::with('organization')->findOrFail($saleId);

            $this->info("Syncing payment status for sale ID: {$saleId}");

            if ($isDryRun) {
                $this->info("  🔍 Would sync payment status");
                return 0;
            }

            $result = $this->syncPaymentStatus->perform($sale);

            if ($result['success']) {
                $this->info("  ✅ Payment status synced successfully");
                $this->info("  Status: {$sale->fresh()->payment_status->value}");
            } else {
                $this->error("  ❌ Failed to sync: {$result['message']}");
                return 1;
            }

            return 0;

        } catch (\Exception $e) {
            $this->error("Error syncing sale {$saleId}: {$e->getMessage()}");
            return 1;
        }
    }

    /**
     * Sync organization payments
     */
    protected function syncOrganizationPayments(int $organizationId, bool $isDryRun): int
    {
        $limit = (int) $this->option('limit');

        $this->info("Syncing payments for organization ID: {$organizationId} (limit: {$limit})");

        if ($isDryRun) {
            $sales = Sale::where('organization_id', $organizationId)
                ->whereNotNull('asaas_payment_id')
                ->limit($limit)
                ->count();

            $this->info("  🔍 Would sync {$sales} payment(s)");
            return 0;
        }

        $result = $this->syncPaymentStatus->syncForOrganization($organizationId, null, $limit);

        $this->info("  ✅ Synced: {$result['total_synced']}");
        $this->info("  ❌ Errors: {$result['total_errors']}");

        if ($result['total_errors'] > 0) {
            $this->warn("Some payments failed to sync. Check logs for details.");
        }

        return $result['total_errors'] > 0 ? 1 : 0;
    }

    /**
     * Sync all payments
     */
    protected function syncAllPayments(bool $isDryRun): int
    {
        $limit = (int) $this->option('limit');

        $organizations = Organization::whereNotNull('asaas_account_id')->get();

        $this->info("Syncing payments for {$organizations->count()} organization(s) (limit: {$limit} per org)");
        $this->newLine();

        $totalSynced = 0;
        $totalErrors = 0;

        foreach ($organizations as $organization) {
            $this->info("Processing: {$organization->name} (ID: {$organization->id})");

            if ($isDryRun) {
                $count = Sale::where('organization_id', $organization->id)
                    ->whereNotNull('asaas_payment_id')
                    ->limit($limit)
                    ->count();

                $this->info("  🔍 Would sync {$count} payment(s)");
                $totalSynced += $count;
                continue;
            }

            $result = $this->syncPaymentStatus->syncForOrganization($organization->id, null, $limit);

            $this->info("  ✅ Synced: {$result['total_synced']}");
            $this->info("  ❌ Errors: {$result['total_errors']}");

            $totalSynced += $result['total_synced'];
            $totalErrors += $result['total_errors'];
        }

        $this->newLine();
        $this->info("Total synced: {$totalSynced}");
        $this->info("Total errors: {$totalErrors}");

        return $totalErrors > 0 ? 1 : 0;
    }

    /**
     * Create customers for clients
     */
    protected function createCustomers(bool $isDryRun): int
    {
        if ($clientId = $this->option('client')) {
            return $this->createSingleCustomer($clientId, $isDryRun);
        }

        if ($organizationId = $this->option('organization')) {
            return $this->createOrganizationCustomers($organizationId, $isDryRun);
        }

        if ($this->option('all')) {
            return $this->createAllCustomers($isDryRun);
        }

        $this->error('Please specify --client=ID, --organization=ID, or --all');
        return 1;
    }

    /**
     * Create single customer
     */
    protected function createSingleCustomer(int $clientId, bool $isDryRun): int
    {
        try {
            $client = Client::with('organization')->findOrFail($clientId);

            $this->info("Creating ASAAS customer for client ID: {$clientId}");

            if (!$this->createCustomer->canCreateCustomer($client)) {
                $this->warn("  ⚠️  Client cannot create ASAAS customer - missing required data or already exists");
                return 0;
            }

            if ($isDryRun) {
                $this->info("  🔍 Would create ASAAS customer");
                return 0;
            }

            $result = $this->createCustomer->perform($client);

            if ($result['success']) {
                $this->info("  ✅ Customer created: {$result['asaas_response']['id']}");
            } else {
                $this->error("  ❌ Failed to create customer");
                return 1;
            }

            return 0;

        } catch (\Exception $e) {
            $this->error("Error creating customer for client {$clientId}: {$e->getMessage()}");
            return 1;
        }
    }

    /**
     * Create customers for organization
     */
    protected function createOrganizationCustomers(int $organizationId, bool $isDryRun): int
    {
        $this->info("Creating ASAAS customers for organization ID: {$organizationId}");

        if ($isDryRun) {
            $count = Client::where('organization_id', $organizationId)
                ->whereNull('asaas_customer_id')
                ->whereNotNull('name')
                ->whereNotNull('email')
                ->where(function($q) {
                    $q->whereNotNull('cpf')->orWhereNotNull('cnpj');
                })
                ->count();

            $this->info("  🔍 Would create {$count} customer(s)");
            return 0;
        }

        $result = $this->createCustomer->createForOrganization($organizationId);

        $this->info("  ✅ Created: {$result['total_processed']}");
        $this->info("  ❌ Errors: {$result['total_errors']}");

        if ($result['total_errors'] > 0) {
            $this->warn("Some customers failed to create. Check logs for details.");
        }

        return $result['total_errors'] > 0 ? 1 : 0;
    }

    /**
     * Create customers for all organizations
     */
    protected function createAllCustomers(bool $isDryRun): int
    {
        $organizations = Organization::whereNotNull('asaas_account_id')->get();

        $this->info("Creating customers for {$organizations->count()} organization(s)");
        $this->newLine();

        $totalCreated = 0;
        $totalErrors = 0;

        foreach ($organizations as $organization) {
            $this->info("Processing: {$organization->name} (ID: {$organization->id})");

            if ($isDryRun) {
                $count = Client::where('organization_id', $organization->id)
                    ->whereNull('asaas_customer_id')
                    ->whereNotNull('name')
                    ->whereNotNull('email')
                    ->where(function($q) {
                        $q->whereNotNull('cpf')->orWhereNotNull('cnpj');
                    })
                    ->count();

                $this->info("  🔍 Would create {$count} customer(s)");
                $totalCreated += $count;
                continue;
            }

            $result = $this->createCustomer->createForOrganization($organization->id);

            $this->info("  ✅ Created: {$result['total_processed']}");
            $this->info("  ❌ Errors: {$result['total_errors']}");

            $totalCreated += $result['total_processed'];
            $totalErrors += $result['total_errors'];
        }

        $this->newLine();
        $this->info("Total created: {$totalCreated}");
        $this->info("Total errors: {$totalErrors}");

        return $totalErrors > 0 ? 1 : 0;
    }

    /**
     * Sync overdue payments
     */
    protected function syncOverduePayments(bool $isDryRun): int
    {
        $limit = (int) $this->option('limit');

        $this->info("Syncing overdue payments (limit: {$limit})");

        if ($isDryRun) {
            $count = Sale::whereNotNull('asaas_payment_id')
                ->where('payment_status', 'pending')
                ->where('due_date', '<', now())
                ->limit($limit)
                ->count();

            $this->info("  🔍 Would sync {$count} overdue payment(s)");
            return 0;
        }

        $result = $this->syncPaymentStatus->syncOverduePayments(null, $limit);

        $this->info("  ✅ Synced: {$result['total_synced']}");
        $this->info("  ❌ Errors: {$result['total_errors']}");

        if ($result['total_errors'] > 0) {
            $this->warn("Some overdue payments failed to sync. Check logs for details.");
        }

        return $result['total_errors'] > 0 ? 1 : 0;
    }
}
