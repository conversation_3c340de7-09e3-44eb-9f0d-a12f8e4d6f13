<?php

namespace App\Domains;

use App\Services\ASAAS\Domains\AsaasOrganization;
use App\Services\ASAAS\Domains\AsaasOrganizationCustomer;
use App\Enums\CompanyType;
use App\Helpers\Traits\PeopleTyped;
use App\Services\ASAAS\Exceptions\AsaasException;
use Carbon\Carbon;

class Organization
{

    use PeopleTyped;

    public const DEFAULT_COMPANY_TYPE = 'MEI';

    public ?int $id;
    public ?string $name;
    public ?string $description;
    public ?bool $is_active;
    public ?bool $is_suspended;
    public ?int $default_flow_id;
    public ?Carbon $created_at;
    public ?Carbon $updated_at;

    // Contact Information
    public ?string $email;
    public ?string $cpf_cnpj;
    public ?CompanyType $company_type;
    public ?string $phone;
    public ?string $mobile_phone;
    public ?string $address;
    public ?string $address_number;
    public ?string $complement;
    public ?string $province;
    public ?string $city;
    public ?string $state;
    public ?string $postal_code;
    public ?Carbon $birth_date;

    // ASAAS Integration Fields
    public ?string $asaas_account_id;
    public ?string $asaas_api_key;
    public ?string $asaas_wallet_id;
    public ?string $asaas_environment;
    public ?string $asaas_subscription_id;
    public ?string $subscription_status;
    public ?float $subscription_value;
    public ?Carbon $subscription_due_date;
    public ?Carbon $subscription_started_at;
    public ?Carbon $subscription_expires_at;

    // Independent Courtesy System
    public ?bool $is_courtesy;
    public ?Carbon $courtesy_expires_at;
    public ?string $courtesy_reason;

    // Financial Information
    public ?float $monthly_revenue;
    public ?float $income_value;

    // ASAAS Relationships
    public ?AsaasOrganization $asaas;
    public ?AsaasOrganizationCustomer $asaasCustomer;

    public function __construct(
        ?int $id,
        ?string $name,
        ?string $description,
        ?bool $is_active,
        ?bool $is_suspended,
        ?int $default_flow_id = null,
        ?string $email = null,
        ?string $cpf_cnpj = null,
        ?CompanyType $company_type = null,
        ?string $phone = null,
        ?string $mobile_phone = null,
        ?string $address = null,
        ?string $address_number = null,
        ?string $complement = null,
        ?string $province = null,
        ?string $city = null,
        ?string $state = null,
        ?string $postal_code = null,
        ?Carbon $birth_date = null,
        ?string $asaas_account_id = null,
        ?string $asaas_api_key = null,
        ?string $asaas_wallet_id = null,
        ?string $asaas_environment = null,
        ?string $asaas_subscription_id = null,
        ?string $subscription_status = null,
        ?float $subscription_value = null,
        ?Carbon $subscription_due_date = null,
        ?Carbon $subscription_started_at = null,
        ?Carbon $subscription_expires_at = null,
        ?bool $is_courtesy = null,
        ?Carbon $courtesy_expires_at = null,
        ?string $courtesy_reason = null,
        ?float $monthly_revenue = null,
        ?float $income_value = null,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null,
        ?AsaasOrganization $asaas = null,
        ?AsaasOrganizationCustomer $asaasCustomer = null,
    ){
        $this->id = $id;
        $this->name = $name;
        $this->description = $description;
        $this->is_active = $is_active;
        $this->is_suspended = $is_suspended;
        $this->default_flow_id = $default_flow_id;
        $this->email = $email;
        $this->cpf_cnpj = $cpf_cnpj;
        $this->company_type = $company_type;
        $this->phone = $phone;
        $this->mobile_phone = $mobile_phone;
        $this->address = $address;
        $this->address_number = $address_number;
        $this->complement = $complement;
        $this->province = $province;
        $this->city = $city;
        $this->state = $state;
        $this->postal_code = $postal_code;
        $this->birth_date = $birth_date;
        $this->asaas_account_id = $asaas_account_id;
        $this->asaas_api_key = $asaas_api_key;
        $this->asaas_wallet_id = $asaas_wallet_id;
        $this->asaas_environment = $asaas_environment;
        $this->asaas_subscription_id = $asaas_subscription_id;
        $this->subscription_status = $subscription_status;
        $this->subscription_value = $subscription_value;
        $this->subscription_due_date = $subscription_due_date;
        $this->subscription_started_at = $subscription_started_at;
        $this->subscription_expires_at = $subscription_expires_at;
        $this->is_courtesy = $is_courtesy;
        $this->courtesy_expires_at = $courtesy_expires_at;
        $this->courtesy_reason = $courtesy_reason;
        $this->monthly_revenue = $monthly_revenue;
        $this->income_value = $income_value;
        $this->asaas = $asaas;
        $this->asaasCustomer = $asaasCustomer;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;

        $this->document = $this->cpf_cnpj;
        $this->setDocumentOnlyNumbers();
    }

    public function toArray(): array
    {
        return [
            "id" => $this->id,
            "name" => $this->name,
            "description" => $this->description,
            "is_active" => $this->is_active,
            "is_suspended" => $this->is_suspended,
            "default_flow_id" => $this->default_flow_id,
            "email" => $this->email,
            "cpf_cnpj" => $this->cpf_cnpj,
            "company_type" => $this->company_type?->value,
            "phone" => $this->phone,
            "mobile_phone" => $this->mobile_phone,
            "address" => $this->address,
            "address_number" => $this->address_number,
            "complement" => $this->complement,
            "province" => $this->province,
            "city" => $this->city,
            "state" => $this->state,
            "postal_code" => $this->postal_code,
            "birth_date" => $this->birth_date?->format("Y-m-d"),
            "asaas_account_id" => $this->asaas_account_id,
            "asaas_api_key" => $this->asaas_api_key,
            "asaas_wallet_id" => $this->asaas_wallet_id,
            "asaas_environment" => $this->asaas_environment,
            "asaas_subscription_id" => $this->asaas_subscription_id,
            "subscription_status" => $this->subscription_status,
            "subscription_value" => $this->subscription_value,
            "subscription_due_date" => $this->subscription_due_date?->format("Y-m-d"),
            "subscription_started_at" => $this->subscription_started_at?->format("Y-m-d H:i:s"),
            "subscription_expires_at" => $this->subscription_expires_at?->format("Y-m-d H:i:s"),
            "is_courtesy" => $this->is_courtesy,
            "courtesy_expires_at" => $this->courtesy_expires_at?->format("Y-m-d"),
            "courtesy_reason" => $this->courtesy_reason,
            "monthly_revenue" => $this->monthly_revenue,
            "income_value" => $this->income_value,
            "created_at" => $this->created_at?->format("Y-m-d H:i:s"),
            "updated_at" => $this->updated_at?->format("Y-m-d H:i:s")
        ];
    }

    public function toAsaasUpdateArray(): array
    {
        return array_filter([
            "asaas_account_id" => $this->asaas_account_id,
            "asaas_api_key" => $this->asaas_api_key,
            "asaas_wallet_id" => $this->asaas_wallet_id,
            "asaas_environment" => $this->asaas_environment,
            "asaas_subscription_id" => $this->asaas_subscription_id,
            "subscription_status" => $this->subscription_status,
            "subscription_value" => $this->subscription_value,
            "subscription_due_date" => $this->subscription_due_date,
            "subscription_started_at" => $this->subscription_started_at,
            "subscription_expires_at" => $this->subscription_expires_at,
            "is_courtesy" => $this->is_courtesy,
            "courtesy_expires_at" => $this->courtesy_expires_at,
            "courtesy_reason" => $this->courtesy_reason,
            "monthly_revenue" => $this->monthly_revenue,
            "income_value" => $this->income_value,
        ], fn ($value) => $value !== null);
    }

    public function toStoreArray(): array
    {
        return array_filter([
            "name" => $this->name,
            "description" => $this->description,
            "is_active" => $this->is_active,
            "is_suspended" => $this->is_suspended,
            "default_flow_id" => $this->default_flow_id,
            "email" => $this->email,
            "cpf_cnpj" => $this->cpf_cnpj,
            "company_type" => $this->company_type,
            "phone" => $this->phone,
            "mobile_phone" => $this->mobile_phone,
            "address" => $this->address,
            "address_number" => $this->address_number,
            "complement" => $this->complement,
            "province" => $this->province,
            "city" => $this->city,
            "state" => $this->state,
            "postal_code" => $this->postal_code,
            "birth_date" => $this->birth_date,
            "asaas_account_id" => $this->asaas_account_id,
            "asaas_api_key" => $this->asaas_api_key,
            "asaas_wallet_id" => $this->asaas_wallet_id,
            "asaas_environment" => $this->asaas_environment,
            "asaas_subscription_id" => $this->asaas_subscription_id,
            "subscription_status" => $this->subscription_status,
            "subscription_value" => $this->subscription_value,
            "subscription_due_date" => $this->subscription_due_date,
            "subscription_started_at" => $this->subscription_started_at,
            "subscription_expires_at" => $this->subscription_expires_at,
            "is_courtesy" => $this->is_courtesy,
            "courtesy_expires_at" => $this->courtesy_expires_at,
            "courtesy_reason" => $this->courtesy_reason,
            "monthly_revenue" => $this->monthly_revenue,
            "income_value" => $this->income_value,
        ], fn ($value) => $value !== null);
    }

    public function toSimpleUpdateArray(): array
    {
        return [
            "name" => $this->name,
            "description" => $this->description,
            "is_active" => $this->is_active,
            "is_suspended" => $this->is_suspended,
            "default_flow_id" => $this->default_flow_id,
            "email" => $this->email,
            "cpf_cnpj" => $this->cpf_cnpj,
            "company_type" => $this->company_type,
            "phone" => $this->phone,
            "mobile_phone" => $this->mobile_phone,
            "address" => $this->address,
            "address_number" => $this->address_number,
            "complement" => $this->complement,
            "province" => $this->province,
            "city" => $this->city,
            "state" => $this->state,
            "postal_code" => $this->postal_code,
            "birth_date" => $this->birth_date,
            "monthly_revenue" => $this->monthly_revenue,
            "income_value" => $this->income_value,
        ];
    }

    public function toUpdateArray(): array
    {
        $updateData = [];

        if ($this->name !== null) $updateData["name"] = $this->name;
        if ($this->description !== null) $updateData["description"] = $this->description;
        if ($this->is_active !== null) $updateData["is_active"] = $this->is_active;
        if ($this->is_suspended !== null) $updateData["is_suspended"] = $this->is_suspended;
        if ($this->default_flow_id !== null) $updateData["default_flow_id"] = $this->default_flow_id;
        if ($this->email !== null) $updateData["email"] = $this->email;
        if ($this->cpf_cnpj !== null) $updateData["cpf_cnpj"] = $this->cpf_cnpj;
        if ($this->company_type !== null) $updateData["company_type"] = $this->company_type;
        if ($this->phone !== null) $updateData["phone"] = $this->phone;
        if ($this->mobile_phone !== null) $updateData["mobile_phone"] = $this->mobile_phone;
        if ($this->address !== null) $updateData["address"] = $this->address;
        if ($this->address_number !== null) $updateData["address_number"] = $this->address_number;
        if ($this->complement !== null) $updateData["complement"] = $this->complement;
        if ($this->province !== null) $updateData["province"] = $this->province;
        if ($this->city !== null) $updateData["city"] = $this->city;
        if ($this->state !== null) $updateData["state"] = $this->state;
        if ($this->postal_code !== null) $updateData["postal_code"] = $this->postal_code;
        if ($this->birth_date !== null) $updateData["birth_date"] = $this->birth_date;
        if ($this->asaas_account_id !== null) $updateData["asaas_account_id"] = $this->asaas_account_id;
        if ($this->asaas_api_key !== null) $updateData["asaas_api_key"] = $this->asaas_api_key;
        if ($this->asaas_wallet_id !== null) $updateData["asaas_wallet_id"] = $this->asaas_wallet_id;
        if ($this->asaas_environment !== null) $updateData["asaas_environment"] = $this->asaas_environment;
        if ($this->asaas_subscription_id !== null) $updateData["asaas_subscription_id"] = $this->asaas_subscription_id;
        if ($this->subscription_status !== null) $updateData["subscription_status"] = $this->subscription_status;
        if ($this->subscription_value !== null) $updateData["subscription_value"] = $this->subscription_value;
        if ($this->subscription_due_date !== null) $updateData["subscription_due_date"] = $this->subscription_due_date;
        if ($this->subscription_started_at !== null) $updateData["subscription_started_at"] = $this->subscription_started_at;
        if ($this->subscription_expires_at !== null) $updateData["subscription_expires_at"] = $this->subscription_expires_at;
        if ($this->is_courtesy !== null) $updateData["is_courtesy"] = $this->is_courtesy;
        if ($this->courtesy_expires_at !== null) $updateData["courtesy_expires_at"] = $this->courtesy_expires_at;
        if ($this->courtesy_reason !== null) $updateData["courtesy_reason"] = $this->courtesy_reason;
        if ($this->monthly_revenue !== null) $updateData["monthly_revenue"] = $this->monthly_revenue;
        if ($this->income_value !== null) $updateData["income_value"] = $this->income_value;

        return $updateData;
    }

    // ========== ASAAS INTEGRATION METHODS ==========

    /**
     * Check if has ASAAS integration
     */
    public function hasAsaasIntegration(): bool
    {
        return $this->asaas !== null && $this->asaas->hasAsaasIntegration();
    }

    /**
     * Check if has ASAAS customer integration
     */
    public function hasAsaasCustomerIntegration(): bool
    {
        return $this->asaasCustomer !== null && $this->asaasCustomer->hasAsaasIntegration();
    }

    /**
     * Get ASAAS subscription summary
     */
    public function getAsaasSubscriptionSummary(): ?array
    {
        // TODO: Get subscription summary from AsaasSubscription repository
        return [$this->asaas?->hasActiveSubscription()];
    }


    /**
     * Check if has active subscription
     */
    public function hasActiveSubscription(): bool
    {
        return $this->asaas?->hasActiveSubscription() ?? false;
    }

    /**
     * Check if organization is in local courtesy period (independent of ASAAS)
     */
    public function isInLocalCourtesy(): bool
    {
        if (!$this->is_courtesy) {
            return false;
        }

        // If no expiration date, courtesy is permanent
        if (!$this->courtesy_expires_at) {
            return true;
        }

        // Check if courtesy hasn't expired
        return $this->courtesy_expires_at->isFuture();
    }

    /**
     * Check if organization can access a system (local courtesy OR ASAAS)
     */
    public function canAccessSystem(): bool
    {
        // Check local courtesy first
        if ($this->isInLocalCourtesy()) {
            return true;
        }

        // Check ASAAS access
        return $this->asaas?->hasAsaasIntegration() ?? false;
    }

    public function setAsaasIntegration(AsaasOrganization $asaasOrganization): void
    {
        $this->asaas = $asaasOrganization;
        $this->asaas_account_id = $asaasOrganization->asaas_account_id;
        $this->asaas_api_key = $asaasOrganization->asaas_api_key;
        $this->asaas_wallet_id = $asaasOrganization->asaas_wallet_id;
        $this->asaas_environment = $asaasOrganization->asaas_environment->value;
    }

    /**
     * Clean postal code removing special characters
     *
     * @return string
     */
    protected function cleanPostalCode(): string
    {
        return preg_replace('/[^0-9]/', '', $this->postal_code);
    }

    public function toAsaasPayload(): array
    {
        return array_filter([
            'name' => $this->name,
            'email' => $this->email,
            'cpfCnpj' => $this->document,
            'companyType' => $this->company_type?->value ?? self::DEFAULT_COMPANY_TYPE,
            'birthDate' => $this->birth_date?->format('Y-m-d'),
            'phone' => $this->phone,
            'mobilePhone' => $this->mobile_phone,
            'address' => $this->address,
            'addressNumber' => $this->address_number,
            'complement' => $this->complement,
            'province' => $this->province,
            'city' => $this->city,
            'state' => $this->state,
            'country' => 'Brasil',
            'postalCode' => $this->cleanPostalCode(),
            'externalReference' => $this->id,
            'incomeValue' => $this->income_value,
            'description'       => $this->description,
        ], fn ($value) => $value !== null);
    }

    /**
     * Convert organization data to ASAAS customer payload format
     */
    public function toAsaasCustomerPayload(): array
    {
        $payload = [
            'name' => $this->name,
            'cpfCnpj' => preg_replace('/\D/', '', $this->cpf_cnpj ?? ''),
        ];

        // Add optional fields if available
        if ($this->email) {
            $payload['email'] = $this->email;
        }

        if ($this->phone) {
            $payload['phone'] = preg_replace('/\D/', '', $this->phone);
        }

        if ($this->mobile_phone) {
            $payload['mobilePhone'] = preg_replace('/\D/', '', $this->mobile_phone);
        }

        if ($this->postal_code) {
            $payload['postalCode'] = preg_replace('/\D/', '', $this->postal_code);
        }

        if ($this->address) {
            $payload['address'] = $this->address;
        }

        if ($this->address_number) {
            $payload['addressNumber'] = $this->address_number;
        }

        if ($this->complement) {
            $payload['complement'] = $this->complement;
        }

        if ($this->province) {
            $payload['province'] = $this->province;
        }

        if ($this->city) {
            $payload['cityName'] = $this->city;
        }

        if ($this->state) {
            $payload['state'] = $this->state;
        }

        // Set external reference to organization ID
        $payload['externalReference'] = (string) $this->id;

        // Set default values
        $payload['notificationDisabled'] = false;
        $payload['foreignCustomer'] = false;

        if ($this->description) {
            $payload['observations'] = $this->description;
        }

        return $payload;
    }

    /**
     * Validate organization data for ASAAS subaccount creation
     *
     * @throws AsaasException validateAsaasPayload
     */
    public function validateAsaasPayload(): void
    {
        $errors = [];

        if (empty($this->name)) {
            $errors[] = 'Organization name is required';
        }
        if (empty($this->document)) {
            $errors[] = 'Organization document is required';
        }
        if (!$this->isValidDocument()) {
            $errors[] = 'Organization document (CPF/CNPJ) is invalid';
        }

        // Additional fields can be added to AsaasOrganization later
        if (!empty($errors)) {
            throw new AsaasException('Validation failed: ' . implode(', ', $errors));
        }
    }
}
