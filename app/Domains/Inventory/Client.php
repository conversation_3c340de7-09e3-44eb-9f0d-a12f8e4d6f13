<?php

namespace App\Domains\Inventory;

use App\Domains\Organization;
use App\Services\ASAAS\Domains\AsaasClient;
use App\Helpers\DBLog;
use Carbon\Carbon;

class Client
{
    public ?int $id;
    public ?int $organization_id;
    public string $name;
    public ?string $phone;
    public ?string $email;
    public ?string $profession;
    public ?string $birthdate;
    public ?string $cpf;
    public ?string $cnpj;
    public ?string $service;
    public ?string $address;
    public ?string $number;
    public ?string $neighborhood;
    public ?string $cep;
    public ?string $complement;
    public ?string $civil_state;
    public ?string $description;
    public ?Carbon $created_at;
    public ?Carbon $updated_at;

    public ?Organization $organization;

    // Relacionamento com ASAAS
    public ?AsaasClient $asaas;

    public function __construct(
        ?int $id,
        ?int $organization_id,
        string $name,
        ?string $phone,
        ?string $email,
        ?string $profession,
        ?string $birthdate,
        ?string $cpf,
        ?string $cnpj,
        ?string $service,
        ?string $address,
        ?string $number,
        ?string $neighborhood,
        ?string $cep,
        ?string $complement,
        ?string $civil_state,
        ?string $description,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null,
        ?Organization $organization = null,
        ?AsaasClient $asaas = null,
    ){
        $this->id = $id;
        $this->organization_id = $organization_id;
        $this->name = $name;
        $this->phone = $phone;
        $this->email = $email;
        $this->profession = $profession;
        $this->birthdate = $birthdate;
        $this->cpf = $cpf;
        $this->cnpj = $cnpj;
        $this->service = $service;
        $this->address = $address;
        $this->number = $number;
        $this->neighborhood = $neighborhood;
        $this->cep = $cep;
        $this->complement = $complement;
        $this->civil_state = $civil_state;
        $this->description = $description;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
        $this->organization = $organization;
        $this->asaas = $asaas;
    }

    public function toArray(): array
    {
        return [
            "id" => $this->id,
            "organization_id" => $this->organization_id,
            "name" => $this->name,
            "phone" => $this->phone,
            "email" => $this->email,
            "profession" => $this->profession,
            "birthdate" => $this->birthdate,
            "cpf" => $this->cpf,
            "cnpj" => $this->cnpj,
            "service" => $this->service,
            "address" => $this->address,
            "number" => $this->number,
            "neighborhood" => $this->neighborhood,
            "cep" => $this->cep,
            "complement" => $this->complement,
            "civil_state" => $this->civil_state,
            "description" => $this->description,
            "created_at" => ($this->created_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "updated_at" => ($this->updated_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "international_transformed_phone" => $this->internationalPhone(),
            "asaas" => $this->asaas?->toArray() ?? null,
            "organization" => $this->organization?->toArray() ?? null,
        ];
    }

    public function toStoreArray(): array
    {
        return [
            "organization_id" => $this->organization_id,
            "name" => $this->name,
            "phone" => $this->phone,
            "email" => $this->email,
            "profession" => $this->profession,
            "birthdate" => $this->birthdate,
            "cpf" => $this->cpf,
            "cnpj" => $this->cnpj,
            "service" => $this->service,
            "address" => $this->address,
            "number" => $this->number,
            "neighborhood" => $this->neighborhood,
            "cep" => $this->cep,
            "complement" => $this->complement,
            "civil_state" => $this->civil_state,
            "description" => $this->description,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            "name" => $this->name,
            "phone" => $this->phone,
            "email" => $this->email,
            "profession" => $this->profession,
            "birthdate" => $this->birthdate,
            "cpf" => $this->cpf,
            "cnpj" => $this->cnpj,
            "service" => $this->service,
            "address" => $this->address,
            "number" => $this->number,
            "neighborhood" => $this->neighborhood,
            "cep" => $this->cep,
            "complement" => $this->complement,
            "civil_state" => $this->civil_state,
            "description" => $this->description,
        ];
    }

    public function internationalPhone() : string {
        try{
            $phone = $this->phone;
            if (preg_match('/^\+\d{10,15}$/', $phone)) {
                return $phone;
            }
            $phone = preg_replace('/\D+/', '', $phone);
            $phone = ltrim($phone, '0');
            if (strlen($phone) == 11) {
                return '+55' . $phone;
            }
            return $phone;
        } catch (\Throwable $e) {
            DBLog::logError(
                $e->getMessage(),
                "Client::internationalPhone",
                $this->organization_id ?? null,
                request()->user()->id ?? null,
                $this->id ?? null
            );
            return $this->phone ?? '';
        }

    }

    // ========== ASAAS INTEGRATION METHODS ==========

    /**
     * Check if has ASAAS integration
     */
    public function hasAsaasIntegration(): bool
    {
        return $this->asaas !== null && $this->asaas->hasAsaasIntegration();
    }

    /**
     * Check if needs ASAAS sync
     */
    public function needsAsaasSync(): bool
    {
        return $this->asaas?->needsSync() ?? true;
    }

    /**
     * Check if is valid for ASAAS integration
     */
    public function isValidForAsaas(): bool
    {
        if (!$this->asaas) {
            return !empty($this->name) && !empty($this->email) && !empty($this->getDocument());
        }

        return $this->asaas->hasValidDocument() && $this->asaas->hasValidEmail();
    }

    /**
     * Get ASAAS customer data
     */
    public function getAsaasCustomerData(): array
    {
        return $this->asaas?->toAsaasPayload() ?? [];
    }

    /**
     * Get document (CPF or CNPJ)
     */
    public function getDocument(): ?string
    {
        return $this->cpf ?: $this->cnpj;
    }

    /**
     * Get ASAAS customer ID
     */
    public function getAsaasCustomerId(): ?string
    {
        return $this->asaas?->asaas_customer_id;
    }

    /**
     * Convert client data to ASAAS customer payload format
     */
    public function toAsaasCustomerPayload(): array
    {
        $document = $this->cpf ?: $this->cnpj;
        $payload = [
            'name' => $this->name,
            'email' => $this->email,
            'cpfCnpj' => preg_replace('/\D/', '', $document ?? ''),
        ];

        // Add optional fields if available
        if ($this->phone) {
            $payload['phone'] = preg_replace('/\D/', '', $this->phone);
            $payload['mobilePhone'] = preg_replace('/\D/', '', $this->phone); // Use same phone as mobile
        }

        if ($this->cep) {
            $payload['postalCode'] = preg_replace('/\D/', '', $this->cep);
        }

        if ($this->address) {
            $payload['address'] = $this->address;
        }

        if ($this->number) {
            $payload['addressNumber'] = $this->number;
        }

        if ($this->complement) {
            $payload['complement'] = $this->complement;
        }

        if ($this->neighborhood) {
            $payload['province'] = $this->neighborhood;
        }

        // Set external reference to client ID
        $payload['externalReference'] = (string) $this->id;

        // Set default values
        $payload['notificationDisabled'] = false;
        $payload['foreignCustomer'] = false;

        if ($this->description) {
            $payload['observations'] = $this->description;
        }

        return $payload;
    }

    public function birthDate(): ?Carbon
    {
        return $this->birthdate ? Carbon::parse($this->birthdate) : null;
    }
}
