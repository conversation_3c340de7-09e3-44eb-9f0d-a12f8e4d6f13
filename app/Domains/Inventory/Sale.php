<?php

namespace App\Domains\Inventory;

use App\Services\ASAAS\Domains\AsaasSale;
use App\Domains\User;
use Carbon\Carbon;

class Sale
{
    public ?int $id;
    public ?int $organization_id;
    public ?int $user_id;
    public ?int $shop_id;
    public ?int $client_id;
    public ?float $total_value;
    public ?Carbon $created_at;
    public ?Carbon $updated_at;
    public ?User $user;
    public ?Shop $shop;
    public ?Client $client;

    /** @var Item[] $items */
    public ?array $items;

    // Relacionamento com ASAAS
    public ?AsaasSale $asaas;

    public function __construct(
        ?int $id,
        ?int $organization_id,
        ?int $user_id,
        ?int $shop_id,
        ?int $client_id,
        ?float $total_value,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null,
        ?User $user = null,
        ?Shop $shop = null,
        ?Client $client = null,
        ?array $items = null,
        ?AsaasSale $asaas = null,
    ){
        $this->id = $id;
        $this->organization_id = $organization_id;
        $this->user_id = $user_id;
        $this->shop_id = $shop_id;
        $this->client_id = $client_id;
        $this->total_value = $total_value;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
        $this->user = $user;
        $this->shop = $shop;
        $this->client = $client;
        $this->items = $items;
        $this->asaas = $asaas;
    }

    public function toArray(): array
    {
        return [
            "id" => $this->id,
            "organization_id" => $this->organization_id,
            "user_id" => $this->user_id,
            "shop_id" => $this->shop_id,
            "client_id" => $this->client_id,
            "total_value" => $this->total_value,
            "created_at" => ($this->created_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "updated_at" => ($this->updated_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "user" => ($this->user) ? $this->user->toArray() : null,
            "shop" => ($this->shop) ? $this->shop->toArray() : null,
            "client" => ($this->client) ? $this->client->toArray() : null,
            "items" => $this->items,
        ];
    }

    public function toStoreArray(): array
    {
        return [
            "organization_id" => $this->organization_id,
            "user_id" => $this->user_id,
            "shop_id" => $this->shop_id,
            "client_id" => $this->client_id,
            "total_value" => $this->total_value,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            "user_id" => $this->user_id,
            "shop_id" => $this->shop_id,
            "client_id" => $this->client_id,
            "total_value" => $this->total_value,
        ];
    }

    // ========== ASAAS INTEGRATION METHODS ==========

    /**
     * Check if has ASAAS payment
     */
    public function hasAsaasPayment(): bool
    {
        return $this->asaas !== null && $this->asaas->hasAsaasIntegration();
    }

    /**
     * Check if payment is paid
     */
    public function isPaid(): bool
    {
        return $this->asaas?->isPaid() ?? false;
    }

    /**
     * Check if payment is pending
     */
    public function isPending(): bool
    {
        return $this->asaas?->isPending() ?? false;
    }

    /**
     * Check if payment is overdue
     */
    public function isOverdue(): bool
    {
        return $this->asaas?->isOverdue() ?? false;
    }

    /**
     * Check if needs ASAAS sync
     */
    public function needsAsaasSync(): bool
    {
        return $this->asaas?->needsAsaasSync() ?? true;
    }

    /**
     * Get payment summary
     */
    public function getAsaasPaymentSummary(): array
    {
        return $this->asaas?->getPaymentSummary() ?? [];
    }

    /**
     * Get ASAAS payment ID
     */
    public function getAsaasPaymentId(): ?string
    {
        return $this->asaas?->asaas_payment_id;
    }
}
