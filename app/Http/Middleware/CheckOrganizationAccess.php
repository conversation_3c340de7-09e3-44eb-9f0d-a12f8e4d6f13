<?php

namespace App\Http\Middleware;

use App\Services\ASAAS\UseCases\Deprecated\Organizations\IsAllowedToUseSystem;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class CheckOrganizationAccess
{
    protected IsAllowedToUseSystem $isAllowedToUseSystem;

    public function __construct(IsAllowedToUseSystem $isAllowedToUseSystem)
    {
        $this->isAllowedToUseSystem = $isAllowedToUseSystem;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $mode = 'strict'): Response
    {
        $user = $request->user();

        if (!$user) {
            return $this->handleUnauthorized($request, 'User not authenticated');
        }

        $organization = $user->organization;

        if (!$organization) {
            return $this->handleUnauthorized($request, 'User has no organization');
        }

        // Check if organization is allowed to use the system
        $accessResult = $this->isAllowedToUseSystem->perform($organization);

        if (!$accessResult['allowed']) {
            Log::warning('Organization access denied', [
                'organization_id' => $organization->id,
                'user_id' => $user->id,
                'reason' => $accessResult['reason'],
                'message' => $accessResult['message'],
                'request_url' => $request->url(),
                'mode' => $mode
            ]);

            return $this->handleAccessDenied($request, $accessResult, $mode);
        }

        // Add organization access info to request for use in controllers
        $request->merge([
            'organization_access' => $accessResult,
            'organization_billing_status' => [
                'is_courtesy' => $organization->is_courtesy,
                'subscription_status' => $organization->subscription_status,
                'has_asaas_integration' => !empty($organization->asaas_account_id),
            ]
        ]);

        return $next($request);
    }

    /**
     * Handle unauthorized access
     *
     * @param Request $request
     * @param string $reason
     * @return Response
     */
    protected function handleUnauthorized(Request $request, string $reason): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => 'Unauthorized',
                'message' => $reason,
                'code' => 'UNAUTHORIZED'
            ], 401);
        }

        return redirect()->route('login')->with('error', $reason);
    }

    /**
     * Handle access denied
     *
     * @param Request $request
     * @param array $accessResult
     * @param string $mode
     * @return Response
     */
    protected function handleAccessDenied(Request $request, array $accessResult, string $mode): Response
    {
        $organization = $request->user()->organization;

        // In 'warning' mode, allow access but add warning headers
        if ($mode === 'warning') {
            $response = app()->call(function() use ($request) {
                return app()->handle($request);
            });

            $response->headers->set('X-Organization-Access-Warning', 'true');
            $response->headers->set('X-Organization-Access-Reason', $accessResult['reason']);
            $response->headers->set('X-Organization-Access-Message', $accessResult['message']);

            return $response;
        }

        // Determine redirect URL based on the reason
        $redirectUrl = $this->getRedirectUrl($accessResult['reason'], $organization->id);

        if ($request->expectsJson()) {
            return response()->json([
                'error' => 'Access Denied',
                'message' => $accessResult['message'],
                'reason' => $accessResult['reason'],
                'code' => 'ORGANIZATION_ACCESS_DENIED',
                'details' => $accessResult['details'] ?? [],
                'redirect_url' => $redirectUrl,
                'actions' => $this->getAvailableActions($accessResult['reason'], $organization->id)
            ], 403);
        }

        // For web requests, redirect to appropriate page with error message
        return redirect($redirectUrl)
            ->with('error', $accessResult['message'])
            ->with('access_denied_reason', $accessResult['reason'])
            ->with('organization_id', $organization->id);
    }

    /**
     * Get redirect URL based on access denial reason
     *
     * @param string $reason
     * @param int $organizationId
     * @return string
     */
    protected function getRedirectUrl(string $reason, int $organizationId): string
    {
        switch ($reason) {
            case 'organization_inactive':
            case 'organization_suspended':
                return route('organizations.show', $organizationId);

            case 'no_asaas_integration':
                return route('organizations.billing.setup', $organizationId);

            case 'subscription_inactive':
                return route('organizations.billing.plans', $organizationId);

            case 'subscription_overdue':
            case 'subscription_overdue_expired':
                return route('organizations.billing.overdue', $organizationId);

            case 'subscription_expired':
                return route('organizations.billing.renew', $organizationId);

            case 'courtesy_expired':
                return route('organizations.billing.expired-courtesy', $organizationId);

            default:
                return route('organizations.billing.index', $organizationId);
        }
    }

    /**
     * Get available actions for the user
     *
     * @param string $reason
     * @param int $organizationId
     * @return array
     */
    protected function getAvailableActions(string $reason, int $organizationId): array
    {
        $actions = [];

        switch ($reason) {
            case 'no_asaas_integration':
                $actions[] = [
                    'action' => 'setup_billing',
                    'title' => 'Configurar Cobrança',
                    'url' => route('organizations.billing.setup', $organizationId),
                    'method' => 'GET'
                ];
                break;

            case 'subscription_inactive':
                $actions[] = [
                    'action' => 'choose_plan',
                    'title' => 'Escolher Plano',
                    'url' => route('organizations.billing.plans', $organizationId),
                    'method' => 'GET'
                ];
                break;

            case 'subscription_overdue':
            case 'subscription_overdue_expired':
                $actions[] = [
                    'action' => 'pay_overdue',
                    'title' => 'Pagar Pendência',
                    'url' => route('organizations.billing.overdue', $organizationId),
                    'method' => 'GET'
                ];
                break;

            case 'subscription_expired':
                $actions[] = [
                    'action' => 'renew_subscription',
                    'title' => 'Renovar Assinatura',
                    'url' => route('organizations.billing.renew', $organizationId),
                    'method' => 'GET'
                ];
                break;

            case 'courtesy_expired':
                $actions[] = [
                    'action' => 'create_subscription',
                    'title' => 'Criar Assinatura',
                    'url' => route('organizations.billing.plans', $organizationId),
                    'method' => 'GET'
                ];
                break;
        }

        // Always add contact support option
        $actions[] = [
            'action' => 'contact_support',
            'title' => 'Entrar em Contato',
            'url' => route('support.contact'),
            'method' => 'GET'
        ];

        return $actions;
    }

    /**
     * Check if route should be exempted from access control
     *
     * @param Request $request
     * @return bool
     */
    protected function isExemptedRoute(Request $request): bool
    {
        $exemptedRoutes = [
            'organizations.billing.*',
            'organizations.show',
            'logout',
            'support.*',
            'profile.*'
        ];

        $currentRoute = $request->route()?->getName();

        if (!$currentRoute) {
            return false;
        }

        foreach ($exemptedRoutes as $pattern) {
            if (fnmatch($pattern, $currentRoute)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Create middleware instance with specific mode
     *
     * @param string $mode
     * @return string
     */
    public static function mode(string $mode): string
    {
        return static::class . ':' . $mode;
    }
}
