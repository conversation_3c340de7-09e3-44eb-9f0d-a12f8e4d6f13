<?php

namespace App\Http\Controllers;

use App\Helpers\Traits\Response;
use App\Http\Requests\Organization\UpdateRequest;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Services\ASAAS\UseCases\Deprecated\Organizations\CheckSubscriptionStatus;
use App\Services\ASAAS\UseCases\Deprecated\Organizations\CreateSubaccount;
use App\Services\ASAAS\UseCases\Deprecated\Organizations\CreateSystemSubscription;
use App\Services\ASAAS\UseCases\Deprecated\Organizations\GetBillingDetails;
use App\UseCases\Organization\Get;
use App\UseCases\Organization\GetAll;
use App\UseCases\Organization\Update;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class OrganizationController extends Controller
{
    use Response;

    public function index() : JsonResponse {
        try{
            /** @var GetAll $useCase */
            $useCase = app()->make(GetAll::class);
            $getAll = $useCase->perform();

            $data = $getAll['data'] ?? [];
            unset($getAll['data']);

            return $this->response(
                "Authorized",
                "success",
                200 ,
                $data ?? [],
                null,
                $getAll ?? []
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function store() : JsonResponse {
        try{
            return $this->response("endpoint unavailable");
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function show(int $id) : JsonResponse {
        try{
            /** @var Get $useCase */
            $useCase = app()->make(Get::class);
            $data = $useCase->perform($id);

            return $this->response(
                "Authorized",
                "success",
                200 ,
                $data->toArray()
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function getToASAASPayload(int $id) : JsonResponse {
        try{
            /** @var Get $useCase */
            $useCase = app()->make(Get::class);
            $organization = $useCase->perform($id);

            return $this->response(
                "Authorized",
                "success",
                200 ,
                $organization->toAsaasPayload()
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function update(UpdateRequest $request, int $id) : JsonResponse {
        try{
            /** @var Update $useCase */
            $useCase = app()->make(Update::class);
            $organization = $useCase->perform($request, $id);

            return $this->response(
                "Organization updated successfully",
                "success",
                200,
                $organization->toArray()
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function destroy() : JsonResponse {
        try{
            return $this->response("endpoint unavailable");
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Get billing details for organization
     */
    public function billing(int $id, GetBillingDetails $getBillingDetails): JsonResponse
    {
        try {
            /** @var Get $getOrganizationUseCase */
            $getOrganizationUseCase = app()->make(Get::class);
            $organization = $getOrganizationUseCase->perform($id);

            $billingDetails = $getBillingDetails->perform($organization);

            return $this->response(
                "Billing details retrieved successfully",
                "success",
                200,
                $billingDetails
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Create ASAAS subaccount for organization
     */
    public function createAsaasSubaccount(int $id, CreateSubaccount $createSubaccount): JsonResponse
    {
        try {
            /** @var Get $getOrganizationUseCase */
            $getOrganizationUseCase = app()->make(Get::class);
            $organization = $getOrganizationUseCase->perform($id);

            $result = $createSubaccount->perform($organization);

            return $this->response(
                $result['message'],
                "success",
                201,
                [
                    'organization' => $result['organization']->toArray(),
                    'asaas_account_id' => $result['asaas_response']['id'] ?? null
                ]
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Create system subscription for organization
     */
    public function createSubscription(int $id, Request $request, CreateSystemSubscription $createSubscription): JsonResponse
    {
        try {
            /** @var Get $getOrganizationUseCase */
            $getOrganizationUseCase = app()->make(Get::class);
            $organization = $getOrganizationUseCase->perform($id);

            $value = $request->input('value', $createSubscription->calculateSubscriptionValue($organization));
            $billingType = $request->input('billing_type', 'BOLETO');
            $options = $request->input('options', []);

            $result = $createSubscription->perform($organization, $value, $billingType, $options);

            return $this->response(
                $result['message'],
                "success",
                201,
                [
                    'organization' => $result['organization']->toArray(),
                    'subscription' => $result['subscription']
                ]
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Check subscription status from ASAAS
     */
    public function checkSubscriptionStatus(int $id, CheckSubscriptionStatus $checkStatus): JsonResponse
    {
        try {
            /** @var Get $getOrganizationUseCase */
            $getOrganizationUseCase = app()->make(Get::class);
            $organization = $getOrganizationUseCase->perform($id);

            $result = $checkStatus->perform($organization);

            return $this->response(
                $result['message'],
                $result['success'] ? "success" : "error",
                200,
                $result
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Get available subscription plans
     */
    public function subscriptionPlans(CreateSystemSubscription $createSubscription): JsonResponse
    {
        try {
            $plans = $createSubscription->getAvailablePlans();

            return $this->response(
                "Subscription plans retrieved successfully",
                "success",
                200,
                $plans
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function checkAccess(int $id): JsonResponse
    {
        try {
            /** @var Get $getOrganizationUseCase */
            $getOrganizationUseCase = app()->make(Get::class);
            $organization = $getOrganizationUseCase->perform($id);

            return $this->response(
                "Organization access checked",
                "success",
                200,
                [$organization->canAccessSystem()]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }
}
