<?php

namespace App\Http\Controllers\ASAAS;

use App\Helpers\Traits\Response;
use App\Http\Controllers\Controller;
use App\Http\Requests\ASAAS\Subscription\CreateAsaasSubscriptionRequest;
use App\Services\ASAAS\UseCases\Deprecated\Subscriptions\CreateAsaasSubscription;
use App\Services\ASAAS\UseCases\Deprecated\Subscriptions\GetAsaasSubscriptionStatus;
use App\Services\ASAAS\UseCases\Deprecated\Subscriptions\SyncAsaasSubscription;
use Illuminate\Http\JsonResponse;

class OldSubscriptionController extends Controller
{
    use Response;

    /**
     * Create ASAAS subscription for existing subscription
     */
    public function createAsaasSubscription(CreateAsaasSubscriptionRequest $request): JsonResponse
    {
        try {
            /** @var CreateAsaasSubscription $useCase */
            $useCase = app()->make(CreateAsaasSubscription::class);
            $asaasSubscription = $useCase->perform($request->subscription_id);

            return $this->response(
                'ASAAS subscription created successfully',
                'success',
                201,
                $asaasSubscription->toArray()
            );
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Sync subscription with ASAAS
     */
    public function syncSubscription(int $id): JsonResponse
    {
        try {
            /** @var SyncAsaasSubscription $useCase */
            $useCase = app()->make(SyncAsaasSubscription::class);
            $result = $useCase->perform($id);

            return $this->response(
                'Subscription synced successfully',
                'success',
                200,
                $result
            );
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Get ASAAS integration status for subscription
     */
    public function getIntegrationStatus(int $id): JsonResponse
    {
        try {
            /** @var GetAsaasSubscriptionStatus $useCase */
            $useCase = app()->make(GetAsaasSubscriptionStatus::class);
            $status = $useCase->perform($id);

            return $this->response(
                'Integration status retrieved successfully',
                'success',
                200,
                $status
            );
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }
}
