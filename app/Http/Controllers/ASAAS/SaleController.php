<?php

namespace App\Http\Controllers\ASAAS;

use App\Helpers\Traits\Response;
use App\Http\Controllers\Controller;
use App\Http\Requests\ASAAS\Sale\CreatePaymentRequest;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Services\ASAAS\UseCases\Deprecated\Sales\CreatePayment;
use App\Services\ASAAS\UseCases\Deprecated\Sales\GetIntegrationStatus;
use App\UseCases\Inventory\Sale\Get;
use Illuminate\Http\JsonResponse;

class SaleController extends Controller
{
    use Response;

    /**
     * Create ASAAS payment for existing sale
     */
    public function createPayment(CreatePaymentRequest $request): JsonResponse
    {
        try {
            /** @var Get $getSaleUseCase */
            $getSaleUseCase = app()->make(Get::class);
            $sale = $getSaleUseCase->perform($request->sale_id);

            $createPayment = app()->make(CreatePayment::class);
            $result = $createPayment->perform($sale);

            return $this->response(
                $result['message'],
                "success",
                201,
                [
                    'sale_id' => $sale->id,
                    'asaas_payment_id' => $result['asaas_response']['id'] ?? null,
                    'organization_id' => $sale->organization_id,
                    'payment_url' => $result['asaas_response']['invoiceUrl'] ?? null
                ]
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Get integration status for sale
     */
    public function getIntegrationStatus(int $id): JsonResponse
    {
        try {
            $sale = Sale::findOrFail($id);

            $getStatus = app()->make(GetIntegrationStatus::class);
            $status = $getStatus->perform($sale);

            return $this->response(
                "Integration status retrieved successfully",
                "success",
                200,
                $status
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }
}
