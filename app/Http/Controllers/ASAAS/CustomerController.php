<?php

namespace App\Http\Controllers\ASAAS;

use App\Http\Controllers\Controller;
use App\Helpers\Traits\Response;
use App\Services\ASAAS\UseCases\Customer\CreateCustomer;
use App\Services\ASAAS\UseCases\Customer\GetAllCustomers;
use App\Services\ASAAS\UseCases\Customer\GetCustomerById;
use App\Services\ASAAS\UseCases\Customer\UpdateCustomer;
use App\Services\ASAAS\UseCases\Customer\DeleteCustomer;
use App\Services\ASAAS\UseCases\Customer\SearchCustomerByEmail;
use App\Services\ASAAS\UseCases\Customer\SearchCustomerByDocument;
use App\Services\ASAAS\UseCases\Customer\GetCustomerNotifications;
use App\Services\ASAAS\Exceptions\AsaasException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CustomerController extends Controller
{
    use Response;

    /**
     * Create a customer
     */
    public function create(Request $request): JsonResponse
    {
        try {
            /** @var CreateCustomer $useCase */
            $useCase = app()->make(CreateCustomer::class);
            $customer = $useCase->perform($request->all());

            return $this->response(
                "Customer created successfully",
                "success",
                201,
                $customer
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Get all customers
     */
    public function index(Request $request): JsonResponse
    {
        try {
            /** @var GetAllCustomers $useCase */
            $useCase = app()->make(GetAllCustomers::class);
            $customers = $useCase->perform($request->all(), auth()->id());

            return $this->response(
                "Customers retrieved successfully",
                "success",
                200,
                $customers
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Get customer by ID
     */
    public function show(string $customer_id): JsonResponse
    {
        try {
            /** @var GetCustomerById $useCase */
            $useCase = app()->make(GetCustomerById::class);
            $customer = $useCase->perform($customer_id, auth()->id());

            return $this->response(
                "Customer retrieved successfully",
                "success",
                200,
                $customer
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Update customer
     */
    public function update(Request $request, string $customer_id): JsonResponse
    {
        try {
            /** @var UpdateCustomer $useCase */
            $useCase = app()->make(UpdateCustomer::class);
            $customer = $useCase->perform($customer_id, $request->all(), auth()->id());

            return $this->response(
                "Customer updated successfully",
                "success",
                200,
                $customer
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Delete customer
     */
    public function destroy(string $customer_id): JsonResponse
    {
        try {
            /** @var DeleteCustomer $useCase */
            $useCase = app()->make(DeleteCustomer::class);
            $result = $useCase->perform($customer_id, auth()->id());

            return $this->response(
                "Customer deleted successfully",
                "success",
                200,
                $result
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Search customer by email
     */
    public function searchByEmail(Request $request): JsonResponse
    {
        try {
            /** @var SearchCustomerByEmail $useCase */
            $useCase = app()->make(SearchCustomerByEmail::class);
            $customers = $useCase->perform($request->email, auth()->id());

            return $this->response(
                "Customer search completed",
                "success",
                200,
                $customers
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Search customer by document
     */
    public function searchByDocument(Request $request): JsonResponse
    {
        try {
            /** @var SearchCustomerByDocument $useCase */
            $useCase = app()->make(SearchCustomerByDocument::class);
            $customers = $useCase->perform($request->cpf_cnpj, auth()->id());

            return $this->response(
                "Customer search completed",
                "success",
                200,
                $customers
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Get customer notifications
     */
    public function getNotifications(Request $request, string $customer_id): JsonResponse
    {
        try {
            /** @var GetCustomerNotifications $useCase */
            $useCase = app()->make(GetCustomerNotifications::class);
            $notifications = $useCase->perform($customer_id, $request->all(), auth()->id());

            return $this->response(
                "Customer notifications retrieved successfully",
                "success",
                200,
                $notifications
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }
}
