<?php

namespace App\Http\Controllers\ASAAS;

use App\Http\Controllers\Controller;
use App\Helpers\Traits\Response;
use App\Services\ASAAS\UseCases\Customer\CreateCustomerClient;
use App\Services\ASAAS\UseCases\Customer\CreateCustomerOrganization;
use App\Services\ASAAS\UseCases\Customer\GetAllCustomers;
use App\Services\ASAAS\UseCases\Customer\GetCustomerById;
use App\Services\ASAAS\UseCases\Customer\UpdateCustomer;
use App\Services\ASAAS\UseCases\Customer\DeleteCustomer;
use App\Services\ASAAS\UseCases\Customer\SearchCustomerByEmail;
use App\Services\ASAAS\UseCases\Customer\SearchCustomerByDocument;
use App\Services\ASAAS\UseCases\Customer\GetCustomerNotifications;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\UseCases\Inventory\Client\Get as GetClient;
use App\UseCases\Organization\Get as GetOrganization;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CustomerController extends Controller
{
    use Response;

    /**
     * Create a customer
     */
    public function create(Request $request): JsonResponse
    {
        try {
            $organization_id = $request->input('organization_id');
            $client_id = $request->input('client_id');
            if (!$organization_id && !$client_id) {
                throw new \Exception('Either organization_id or client_id must be provided');
            }

            if ($organization_id) {
                /** @var GetOrganization $getOrganizationUseCase */
                $getOrganizationUseCase = app()->make(GetOrganization::class);
                $organization = $getOrganizationUseCase->perform($organization_id);

                /** @var CreateCustomerOrganization $useCase */
                $useCase = app()->make(CreateCustomerOrganization::class);
                $result = $useCase->perform($organization);
            }

            if ($client_id) {
                /** @var GetClient $getClientUseCase */
                $getClientUseCase = app()->make(GetClient::class);
                $client = $getClientUseCase->perform($client_id);

                /** @var CreateCustomerClient $useCase */
                $useCase = app()->make(CreateCustomerClient::class);
                $result = $useCase->perform($client);
            }

            return $this->response(
                    "Customer created successfully",
                    "success",
                    201,
                    [
                        'organization_id' => $organization_id,
                        'client_id' => $client_id ?? null,
                        'asaas_customer_id' => $result['asaas_customer_id'] ?? null,
                        'customer_data' => $result['customer_data'] ?? null
                    ]
                );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Get all customers
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $organization_id = $request->input('organization_id');
            if ($organization_id) {
                /** @var GetOrganization $getOrganizationUseCase */
                $getOrganizationUseCase = app()->make(GetOrganization::class);
                $organization = $getOrganizationUseCase->perform($organization_id);
            }
            /** @var GetAllCustomers $useCase */
            $useCase = app()->make(GetAllCustomers::class);
            $customers = $useCase->perform($request->all(), $organization ?? null);

            return $this->response(
                "Customers retrieved successfully",
                "success",
                200,
                $customers
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Get customer by ID
     */
    public function show(int $client_id): JsonResponse
    {
        try {
            /** @var GetClient $getClientUseCase */
            $getClientUseCase = app()->make(GetClient::class);
            $client = $getClientUseCase->perform($client_id);

            $customer_id = $client->asaas->asaas_customer_id;

            /** @var GetCustomerById $useCase */
            $useCase = app()->make(GetCustomerById::class);
            $customer = $useCase->perform($customer_id);

            return $this->response(
                "Customer retrieved successfully",
                "success",
                200,
                $customer
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Update customer
     */
    public function update(int $client_id): JsonResponse
    {
        try {
            /** @var GetClient $getClientUseCase */
            $getClientUseCase = app()->make(GetClient::class);
            $client = $getClientUseCase->perform($client_id);

            $customer_id = $client->asaas->asaas_customer_id;

            /** @var UpdateCustomer $useCase */
            $useCase = app()->make(UpdateCustomer::class);
            $customer = $useCase->perform($customer_id, $client->toAsaasCustomerPayload());

            return $this->response(
                "Customer updated successfully",
                "success",
                200,
                $customer
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Delete customer
     */
    public function destroy(int $client_id): JsonResponse
    {
        try {
            /** @var GetClient $getClientUseCase */
            $getClientUseCase = app()->make(GetClient::class);
            $client = $getClientUseCase->perform($client_id);

            $customer_id = $client->asaas->asaas_customer_id;

            /** @var DeleteCustomer $useCase */
            $useCase = app()->make(DeleteCustomer::class);
            $result = $useCase->perform($customer_id);

            return $this->response(
                "Customer deleted successfully",
                "success",
                200,
                $result
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Search customer by email
     */
    public function searchByEmail(Request $request): JsonResponse
    {
        try {
            /** @var SearchCustomerByEmail $useCase */
            $useCase = app()->make(SearchCustomerByEmail::class);
            $customers = $useCase->perform($request->email, auth()->id());

            return $this->response(
                "Customer search completed",
                "success",
                200,
                $customers
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Search customer by document
     */
    public function searchByDocument(Request $request): JsonResponse
    {
        try {
            /** @var SearchCustomerByDocument $useCase */
            $useCase = app()->make(SearchCustomerByDocument::class);
            $customers = $useCase->perform($request->cpf_cnpj, auth()->id());

            return $this->response(
                "Customer search completed",
                "success",
                200,
                $customers
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Get customer notifications
     */
    public function getNotifications(Request $request, int $client_id): JsonResponse
    {
        try {
            /** @var GetClient $getClientUseCase */
            $getClientUseCase = app()->make(GetClient::class);
            $client = $getClientUseCase->perform($client_id);

            $customer_id = $client->asaas->asaas_customer_id;

            /** @var GetCustomerNotifications $useCase */
            $useCase = app()->make(GetCustomerNotifications::class);
            $notifications = $useCase->perform($customer_id, $request->all(), auth()->id());

            return $this->response(
                "Customer notifications retrieved successfully",
                "success",
                200,
                $notifications
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }
}
