<?php

namespace App\Http\Controllers\ASAAS;

use App\Helpers\Traits\Response;
use App\Http\Controllers\Controller;
use App\Http\Requests\ASAAS\Client\CreateCustomerRequest;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Services\ASAAS\UseCases\Deprecated\Clients\CreateCustomer;
use App\Services\ASAAS\UseCases\Deprecated\Clients\GetIntegrationStatus;
use App\UseCases\Inventory\Client\Get;
use Illuminate\Http\JsonResponse;

class ClientController extends Controller
{
    use Response;

    /**
     * Create ASAAS customer for existing client
     */
    public function createCustomer(CreateCustomerRequest $request): JsonResponse
    {
        try {
            /** @var Get $getClientUseCase */
            $getClientUseCase = app()->make(Get::class);
            $client = $getClientUseCase->perform($request->client_id);

            $createCustomer = app()->make(CreateCustomer::class);
            $result = $createCustomer->perform($client);

            return $this->response(
                $result['message'],
                "success",
                201,
                [
                    'client_id' => $client->id,
                    'asaas_customer_id' => $result['asaas_response']['id'] ?? null,
                    'organization_id' => $client->organization_id
                ]
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Get integration status for client
     */
    public function getIntegrationStatus(int $id): JsonResponse
    {
        try {
            /** @var Get $getClientUseCase */
            $getClientUseCase = app()->make(Get::class);
            $client = $getClientUseCase->perform($id);

            $getStatus = app()->make(GetIntegrationStatus::class);
            $status = $getStatus->perform($client);

            return $this->response(
                "Integration status retrieved successfully",
                "success",
                200,
                $status
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }
}
