<?php

namespace App\Http\Controllers\ASAAS;

use App\Helpers\Traits\Response;
use App\Http\Controllers\Controller;
use App\Http\Requests\ASAAS\Organization\CreateSubaccountRequest;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Services\ASAAS\UseCases\Deprecated\Organizations\CreateSubaccount;
use App\Services\ASAAS\UseCases\Deprecated\Organizations\GetIntegrationStatus;
use App\UseCases\Organization\Get;
use Illuminate\Http\JsonResponse;

class OrganizationController extends Controller
{
    use Response;

    /**
     * Create ASAAS subaccount for existing organization
     */
    public function createSubaccount(CreateSubaccountRequest $request): JsonResponse
    {
        try {
            /** @var Get $getOrganizationUseCase */
            $getOrganizationUseCase = app()->make(Get::class);
            $organization = $getOrganizationUseCase->perform($request->organization_id);

            /** @var CreateSubaccount $createSubaccount */
            $createSubaccount = app()->make(CreateSubaccount::class);
            $result = $createSubaccount->perform($organization);

            return $this->response(
                $result['message'],
                "success",
                201,
                [
                    'organization_id' => $organization->id,
                    'asaas_account_id' => $result['asaas_response']['id'] ?? null,
                    'asaas_api_key' => $result['asaas_response']['apiKey'] ?? null,
                    'environment' => $result['asaas_response']['environment'] ?? 'sandbox'
                ]
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Get integration status for organization
     */
    public function getIntegrationStatus(int $id): JsonResponse
    {
        try {
            /** @var Get $getOrganizationUseCase */
            $getOrganizationUseCase = app()->make(Get::class);
            $organization = $getOrganizationUseCase->perform($id);

            $getStatus = app()->make(GetIntegrationStatus::class);
            $status = $getStatus->perform($organization);

            return $this->response(
                "Integration status retrieved successfully",
                "success",
                200,
                $status
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function checkAccess(int $id): JsonResponse
    {
        try {
            // TODO: build has subscription on asaas level
            /** @var Get $getOrganizationUseCase */
            $getOrganizationUseCase = app()->make(Get::class);
            $organization = $getOrganizationUseCase->perform($id);
            $asaasOrganization = $organization->asaas;
            return $this->response(
                "Organization access checked",
                "success",
                200,
                [$asaasOrganization && $organization?->hasActiveSubscription()]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }
}
