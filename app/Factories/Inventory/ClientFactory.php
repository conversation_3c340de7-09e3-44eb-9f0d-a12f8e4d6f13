<?php

namespace App\Factories\Inventory;

use App\Domains\Inventory\Client;
use App\Factories\OrganizationFactory;
use App\Http\Requests\Client\StoreRequest;
use App\Http\Requests\Client\UpdateRequest;
use App\Models\Client as ClientModel;
use App\Services\ASAAS\Factories\AsaasClientFactory;
use Illuminate\Support\Collection;

class ClientFactory
{
    private ?AsaasClientFactory $asaasClientFactory = null;
    private ?OrganizationFactory $organizationFactory = null;

    public function buildFromStoreRequest(StoreRequest $request) : Client {
        return new Client(
            null,
            $request->organization_id ?? null,
            $request->name ?? "",
            $request->phone ?? null,
            $request->email ?? null,
            $request->profession ?? null,
            $request->birthdate ?? null,
            $request->cpf ?? null,
            $request->cnpj ?? null,
            $request->service ?? null,
            $request->address ?? null,
            $request->number ?? null,
            $request->neighborhood ?? null,
            $request->cep ?? null,
            $request->complement ?? null,
            $request->civil_state ?? null,
            $request->description ?? null,
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : Client {
        return new Client(
            null,
            null,
            $request->name ?? "",
            $request->phone ?? null,
            $request->email ?? null,
            $request->profession ?? null,
            $request->birthdate ?? null,
            $request->cpf ?? null,
            $request->cnpj ?? null,
            $request->service ?? null,
            $request->address ?? null,
            $request->number ?? null,
            $request->neighborhood ?? null,
            $request->cep ?? null,
            $request->complement ?? null,
            $request->civil_state ?? null,
            $request->description ?? null,
        );
    }

    public function buildFromModel(
        ?ClientModel $client, bool $with_asaas = false, bool $with_organization = false
    ) : ?Client {
        if(!$client){
            return null;
        }

        if ($with_asaas) {
            if (!$this->asaasClientFactory) {  $this->asaasClientFactory = new AsaasClientFactory($this); }
            $asaas = $this->asaasClientFactory->buildFromModel($client->asaas);
        }
        if ($with_organization) {
            if (!$this->organizationFactory) {  $this->organizationFactory = new OrganizationFactory($this); }
            $organization = $this->organizationFactory->buildFromModel($client->organization);
        }

        return new Client(
            $client->id ?? null,
            $client->organization_id ?? null,
            $client->name ?? "",
            $client->phone ?? null,
            $client->email ?? null,
            $client->profession ?? null,
            $client->birthdate ?? null,
            $client->cpf ?? null,
            $client->cnpj ?? null,
            $client->service ?? null,
            $client->address ?? null,
            $client->number ?? null,
            $client->neighborhood ?? null,
            $client->cep ?? null,
            $client->complement ?? null,
            $client->civil_state ?? null,
            $client->description ?? null,
            $client->created_at ?? null,
            $client->updated_at ?? null,
            $organization ?? null,
            $asaas ?? null
        );
    }

    public function buildFromModels(?Collection $clients) : ?array {
        if(!$clients){ return []; }

        $clientsArray = [];
        foreach ($clients as $client){
            $clientsArray[] = $this->buildFromModel($client);
        }
        return $clientsArray;
    }
}
