<?php

namespace Tests\Unit\Services\ASAAS\UseCases\Clients;

use App\Domains\Inventory\Client as ClientDomain;
use App\Domains\Organization as OrganizationDomain;
use App\Enums\AsaasEnvironment;
use App\Models\Client;
use App\Models\Organization;
use App\Repositories\ClientRepository;
use App\Services\ASAAS\AsaasService;
use App\Services\ASAAS\Domains\AsaasClient;
use App\Services\ASAAS\Domains\AsaasOrganization;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Services\ASAAS\Factories\AsaasClientFactory;
use App\Services\ASAAS\Models\AsaasClient as AsaasClientModel;
use App\Services\ASAAS\Models\AsaasOrganization as AsaasOrganizationModel;
use App\Services\ASAAS\Repositories\AsaasClientRepository;
use App\Services\ASAAS\Repositories\AsaasOrganizationRepository;
use App\Services\ASAAS\UseCases\Deprecated\Clients\CreateCustomer;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class CreateCustomerTest extends TestCase
{
    use RefreshDatabase;
    private $asaasService;
    private $asaasClientRepository;
    private $asaasOrganizationRepository;
    private $asaasClientFactory;
    private $clientRepository;
    private CreateCustomer $useCase;

    protected function setUp(): void
    {
        parent::setUp();

        // Only mock external dependencies (AsaasService)
        $this->asaasService = Mockery::mock(AsaasService::class);

        // Use real repositories and factories - these are our domain logic
        $this->asaasClientRepository = app(AsaasClientRepository::class);
        $this->asaasOrganizationRepository = app(AsaasOrganizationRepository::class);
        $this->asaasClientFactory = app(AsaasClientFactory::class);
        $this->clientRepository = app(ClientRepository::class);

        $this->useCase = new CreateCustomer(
            $this->asaasService,
            $this->asaasClientRepository,
            $this->asaasOrganizationRepository,
            $this->asaasClientFactory,
            $this->clientRepository
        );
    }

    /**
     * Create test data in database
     */
    protected function createTestData()
    {
        // Create organization with ASAAS integration
        $organization = Organization::factory()->create();
        $asaasOrganization = AsaasOrganizationModel::factory()->create([
            'organization_id' => $organization->id,
            'asaas_account_id' => 'acc_test123',
            'asaas_api_key' => 'test_api_key',
            'asaas_environment' => AsaasEnvironment::SANDBOX,
        ]);

        // Create client
        $clientModel = Client::factory()->create([
            'organization_id' => $organization->id,
            'name' => 'Test Client',
            'email' => '<EMAIL>',
            'cpf' => '***********',
        ]);

        // Convert to domain object
        $client = new ClientDomain(
            id: $clientModel->id,
            organization_id: $clientModel->organization_id,
            name: $clientModel->name,
            phone: $clientModel->phone,
            email: $clientModel->email,
            profession: $clientModel->profession,
            birthdate: $clientModel->birthdate,
            cpf: $clientModel->cpf,
            cnpj: $clientModel->cnpj,
            service: $clientModel->service,
            address: $clientModel->address,
            number: $clientModel->number,
            neighborhood: $clientModel->neighborhood,
            cep: $clientModel->cep,
            complement: $clientModel->complement,
            civil_state: $clientModel->civil_state,
            description: $clientModel->description,
            created_at: $clientModel->created_at,
            updated_at: $clientModel->updated_at
        );

        return [$organization, $asaasOrganization, $client];
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    private function createClientDomain(array $overrides = []): ClientDomain
    {
        $defaults = [
            'id' => 1,
            'organization_id' => 1,
            'name' => 'Test Client',
            'phone' => '11999999999',
            'email' => '<EMAIL>',
            'profession' => null,
            'birthdate' => null,
            'cpf' => '***********',
            'cnpj' => null,
            'service' => null,
            'address' => 'Test Address',
            'number' => '123',
            'neighborhood' => 'Test Neighborhood',
            'cep' => '01310100',
            'complement' => 'Apt 1',
            'civil_state' => null,
            'description' => null,
        ];

        $data = array_merge($defaults, $overrides);

        return new ClientDomain(
            id: $data['id'],
            organization_id: $data['organization_id'],
            name: $data['name'],
            phone: $data['phone'],
            email: $data['email'],
            profession: $data['profession'],
            birthdate: $data['birthdate'],
            cpf: $data['cpf'],
            cnpj: $data['cnpj'],
            service: $data['service'],
            address: $data['address'],
            number: $data['number'],
            neighborhood: $data['neighborhood'],
            cep: $data['cep'],
            complement: $data['complement'],
            civil_state: $data['civil_state'],
            description: $data['description']
        );
    }

    private function createOrganizationDomain(): OrganizationDomain
    {
        return new OrganizationDomain(
            id: 1,
            name: 'Test Organization',
            description: 'Test Description',
            is_active: true,
            is_suspended: false,
            default_flow_id: null,
            created_at: null,
            updated_at: null
        );
    }

    private function createAsaasOrganization(array $overrides = []): AsaasOrganization
    {
        $defaults = [
            'id' => 1,
            'organization_id' => 1,
            'asaas_account_id' => 'acc_123456',
            'asaas_api_key' => 'test_api_key',
            'asaas_environment' => AsaasEnvironment::SANDBOX,
            'last_sync_at' => Carbon::now(),
            'sync_errors' => null,
        ];

        $data = array_merge($defaults, $overrides);

        return new AsaasOrganization(
            id: $data['id'],
            organization_id: $data['organization_id'],
            organization: null, // Mock organization if needed
            asaas_account_id: $data['asaas_account_id'],
            asaas_api_key: $data['asaas_api_key'],
            asaas_wallet_id: 'wal_123',
            asaas_environment: $data['asaas_environment'],
            is_active: true,
            last_sync_at: $data['last_sync_at'],
            sync_errors: $data['sync_errors']
        );
    }

    private function createAsaasClient(array $overrides = []): AsaasClient
    {
        $defaults = [
            'id' => 1,
            'organization_id' => 1,
            'client_id' => 1,
            'asaas_customer_id' => 'cus_123456',
            'asaas_synced_at' => Carbon::now(),
            'asaas_sync_errors' => null,
        ];

        $data = array_merge($defaults, $overrides);

        return new AsaasClient(
            id: $data['id'],
            client_id: $data['client_id'],
            organization_id: $data['organization_id'],
            asaas_customer_id: $data['asaas_customer_id'],
            asaas_synced_at: $data['asaas_synced_at'],
            asaas_sync_errors: $data['asaas_sync_errors'],
            sync_status: 'synced',
            name: 'Test Client',
            email: '<EMAIL>',
            phone: '11999999999',
            mobile_phone: '11999999999',
            address: 'Test Address',
            address_number: '123',
            complement: null,
            province: 'Test Province',
            city_name: 'Test City',
            state: 'SP',
            country: 'Brasil',
            postal_code: '01234-567',
            cpf_cnpj: '123.456.789-00',
            person_type: 'FISICA',
            external_reference: null,
            notification_disabled: false,
            additional_emails: null,
            observations: null,
            foreign_customer: false,
            deleted: false,
            client: null,
            created_at: Carbon::now(),
            updated_at: Carbon::now()
        );
    }

    public function test_throws_exception_when_client_already_has_asaas_integration()
    {
        [$organization, $asaasOrganization, $client] = $this->createTestData();

        // Create existing AsaasClient for this client
        AsaasClientModel::factory()->create([
            'client_id' => $client->id,
            'organization_id' => $organization->id,
            'asaas_customer_id' => 'existing_customer_123',
        ]);

        $this->expectException(AsaasException::class);
        $this->expectExceptionMessage('Client already has an ASAAS customer integration');

        $this->useCase->perform($client);
    }

    public function test_throws_exception_when_client_missing_required_data()
    {
        [$organization, $asaasOrganization, $client] = $this->createTestData();

        // Update client to have missing required data
        $client = new ClientDomain(
            id: $client->id,
            organization_id: $client->organization_id,
            name: $client->name,
            phone: null, // Missing phone
            email: null, // Missing email
            profession: $client->profession,
            birthdate: $client->birthdate,
            cpf: $client->cpf,
            cnpj: $client->cnpj,
            service: $client->service,
            address: $client->address,
            number: $client->number,
            neighborhood: $client->neighborhood,
            cep: $client->cep,
            complement: $client->complement,
            civil_state: $client->civil_state,
            description: $client->description,
            created_at: $client->created_at,
            updated_at: $client->updated_at
        );

        $this->expectException(AsaasException::class);
        $this->expectExceptionMessage('Validation failed');

        $this->useCase->perform($client);
    }

    public function test_creates_customer_successfully()
    {
        [$organization, $asaasOrganization, $client] = $this->createTestData();

        // Mock ASAAS API response
        $asaasResponse = [
            'id' => 'cus_123456',
            'name' => 'Test Client',
            'email' => '<EMAIL>',
            'cpfCnpj' => '***********',
        ];

        // Mock only the external ASAAS service call
        $this->asaasService
            ->shouldReceive('post')
            ->once()
            ->with(
                '/v3/customers',
                Mockery::type('array'),
                'test_api_key',
                AsaasEnvironment::SANDBOX
            )
            ->andReturn($asaasResponse);

        $result = $this->useCase->perform($client);

        // The method returns an AsaasClient domain object
        $this->assertInstanceOf(\App\Services\ASAAS\Domains\AsaasClient::class, $result);
        $this->assertEquals('cus_123456', $result->asaas_customer_id);

        // Verify the AsaasClient was saved to database
        $this->assertDatabaseHas('asaas_clients', [
            'client_id' => $client->id,
            'asaas_customer_id' => 'cus_123456',
        ]);
    }

    public function test_handles_asaas_api_error()
    {
        [$organization, $asaasOrganization, $client] = $this->createTestData();

        // Mock ASAAS service to throw an exception
        $this->asaasService
            ->shouldReceive('post')
            ->once()
            ->andThrow(new AsaasException('ASAAS API Error', 400));

        $this->expectException(AsaasException::class);
        $this->expectExceptionMessage('ASAAS API Error');

        $this->useCase->perform($client);
    }
}
