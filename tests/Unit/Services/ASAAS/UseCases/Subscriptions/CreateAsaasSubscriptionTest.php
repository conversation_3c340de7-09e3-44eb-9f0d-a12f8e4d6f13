<?php

namespace Tests\Unit\Services\ASAAS\UseCases\Subscriptions;

use App\Domains\Subscription;
use App\Repositories\SubscriptionRepository;
use App\Services\ASAAS\AsaasService;
use App\Services\ASAAS\Domains\AsaasSubscription;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Services\ASAAS\Factories\AsaasSubscriptionFactory;
use App\Services\ASAAS\Repositories\AsaasSubscriptionRepository;
use App\Services\ASAAS\UseCases\Deprecated\Subscriptions\CreateAsaasSubscription;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Mockery;
use Tests\TestCase;

class CreateAsaasSubscriptionTest extends TestCase
{
    private CreateAsaasSubscription $useCase;
    private $asaasService;
    private $asaasSubscriptionRepository;
    private $asaasSubscriptionFactory;
    private $subscriptionRepository;

    protected function setUp(): void
    {
        parent::setUp();

        $this->asaasService = Mockery::mock(AsaasService::class);
        $this->asaasSubscriptionRepository = Mockery::mock(AsaasSubscriptionRepository::class);
        $this->asaasSubscriptionFactory = Mockery::mock(AsaasSubscriptionFactory::class);
        $this->subscriptionRepository = Mockery::mock(SubscriptionRepository::class);

        $this->useCase = new CreateAsaasSubscription(
            $this->subscriptionRepository,
            $this->asaasSubscriptionRepository,
            $this->asaasSubscriptionFactory,
            $this->asaasService
        );
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    private function createSubscription(float $value = 99.90): Subscription
    {
        return new Subscription(
            id: 100,
            organization_id: 1,
            status: 'ACTIVE',
            billing_type: 'BOLETO',
            cycle: 'MONTHLY',
            value: $value,
            started_at: Carbon::now(),
            expires_at: Carbon::parse('2025-12-31'),
            next_due_date: null,
            end_date: null,
            is_courtesy: false,
            courtesy_expires_at: null,
            courtesy_reason: null,
            is_trial: false,
            trial_expires_at: null,
            trial_days: null,
            description: null,
            max_payments: null,
            external_reference: null,
            discount_value: null,
            discount_type: null,
            discount_due_date_limit_days: null,
            fine_value: null,
            interest_value: null,
            deleted: false
        );
    }

    public function test_creates_asaas_subscription_successfully()
    {
        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();
        DB::shouldReceive('rollback')->never();

        $subscriptionId = 100;

        // Create subscription domain
        $subscription = $this->createSubscription();

        // Mock subscription repository
        $this->subscriptionRepository
            ->shouldReceive('findById')
            ->with($subscriptionId)
            ->once()
            ->andReturn($subscription);

        // Mock ASAAS API response
        $asaasResponse = [
            'id' => 'sub_987654321',
            'customer' => 'cus_123456789',
            'dateCreated' => '2025-01-01',
            'billingType' => 'BOLETO',
            'cycle' => 'MONTHLY',
            'value' => 99.90,
            'nextDueDate' => '2025-02-01',
            'status' => 'ACTIVE',
            'description' => 'Assinatura do sistema',
        ];

        $this->asaasService
            ->shouldReceive('post')
            ->with('/v3/subscriptions', [
                'customer' => 1,
                'billingType' => 'BOLETO',
                'value' => 99.90,
                'nextDueDate' => '2025-12-31',
                'cycle' => 'MONTHLY',
                'description' => 'Assinatura do sistema',
            ])
            ->once()
            ->andReturn($asaasResponse);

        // Mock factory
        $asaasSubscription = Mockery::mock(AsaasSubscription::class);
        $asaasSubscription->shouldReceive('getAttribute')->with('id')->andReturn(1);
        $asaasSubscription->shouldReceive('getAttribute')->with('asaas_subscription_id')->andReturn('sub_987654321');

        $this->asaasSubscriptionFactory
            ->shouldReceive('buildFromStoreArray')
            ->withAnyArgs()
            ->once()
            ->andReturn($asaasSubscription);

        // Mock repository store
        $this->asaasSubscriptionRepository
            ->shouldReceive('store')
            ->with($asaasSubscription)
            ->once()
            ->andReturn($asaasSubscription);

        $result = $this->useCase->perform($subscriptionId);

        $this->assertInstanceOf(AsaasSubscription::class, $result);
    }

    public function test_throws_exception_when_subscription_not_found()
    {
        $subscriptionId = 999;

        $this->subscriptionRepository
            ->shouldReceive('findById')
            ->with($subscriptionId)
            ->once()
            ->andReturn(null);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Subscription not found');

        $this->useCase->perform($subscriptionId);
    }

    public function test_handles_asaas_api_error()
    {
        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('rollback')->once();
        DB::shouldReceive('commit')->never();

        $subscriptionId = 100;

        // Create subscription domain
        $subscription = $this->createSubscription();

        $this->subscriptionRepository
            ->shouldReceive('findById')
            ->with($subscriptionId)
            ->once()
            ->andReturn($subscription);

        // Mock ASAAS API error
        $this->asaasService
            ->shouldReceive('post')
            ->with('/v3/subscriptions', Mockery::any())
            ->once()
            ->andThrow(new AsaasException('Customer not found'));

        $this->expectException(AsaasException::class);
        $this->expectExceptionMessage('Customer not found');

        $this->useCase->perform($subscriptionId);
    }

    public function test_handles_repository_error()
    {
        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('rollback')->once();
        DB::shouldReceive('commit')->never();

        $subscriptionId = 100;

        // Create subscription domain
        $subscription = $this->createSubscription();

        $this->subscriptionRepository
            ->shouldReceive('findById')
            ->with($subscriptionId)
            ->once()
            ->andReturn($subscription);

        // Mock ASAAS API response
        $asaasResponse = [
            'id' => 'sub_987654321',
            'customer' => 'cus_123456789',
            'dateCreated' => '2025-01-01',
            'billingType' => 'BOLETO',
            'cycle' => 'MONTHLY',
            'value' => 99.90,
            'nextDueDate' => '2025-02-01',
            'status' => 'ACTIVE',
            'description' => 'Assinatura do sistema',
        ];

        $this->asaasService
            ->shouldReceive('post')
            ->with('/v3/subscriptions', Mockery::any())
            ->once()
            ->andReturn($asaasResponse);

        // Mock factory
        $asaasSubscription = Mockery::mock(AsaasSubscription::class);

        $this->asaasSubscriptionFactory
            ->shouldReceive('buildFromStoreArray')
            ->once()
            ->andReturn($asaasSubscription);

        // Mock repository error
        $this->asaasSubscriptionRepository
            ->shouldReceive('store')
            ->with($asaasSubscription)
            ->once()
            ->andThrow(new \Exception('Database error'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($subscriptionId);
    }

    public function test_creates_subscription_with_custom_billing_type()
    {
        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();
        DB::shouldReceive('rollback')->never();

        $subscriptionId = 100;

        // Create subscription domain
        $subscription = $this->createSubscription(199.90);

        $this->subscriptionRepository
            ->shouldReceive('findById')
            ->with($subscriptionId)
            ->once()
            ->andReturn($subscription);

        // Mock ASAAS API response for credit card
        $asaasResponse = [
            'id' => 'sub_987654321',
            'customer' => 'cus_123456789',
            'dateCreated' => '2025-01-01',
            'billingType' => 'BOLETO',
            'cycle' => 'MONTHLY',
            'value' => 199.90,
            'nextDueDate' => '2025-02-01',
            'status' => 'ACTIVE',
            'description' => 'Assinatura do sistema',
        ];

        $this->asaasService
            ->shouldReceive('post')
            ->with('/v3/subscriptions', [
                'customer' => 1,
                'billingType' => 'BOLETO',
                'value' => 199.90,
                'nextDueDate' => '2025-12-31',
                'cycle' => 'MONTHLY',
                'description' => 'Assinatura do sistema',
            ])
            ->once()
            ->andReturn($asaasResponse);

        // Mock factory
        $asaasSubscription = Mockery::mock(AsaasSubscription::class);
        $asaasSubscription->shouldReceive('getAttribute')->with('id')->andReturn(1);
        $asaasSubscription->shouldReceive('getAttribute')->with('asaas_subscription_id')->andReturn('sub_987654321');

        $this->asaasSubscriptionFactory
            ->shouldReceive('buildFromStoreArray')
            ->once()
            ->andReturn($asaasSubscription);

        $this->asaasSubscriptionRepository
            ->shouldReceive('store')
            ->with($asaasSubscription)
            ->once()
            ->andReturn($asaasSubscription);

        $result = $this->useCase->perform($subscriptionId);

        $this->assertInstanceOf(AsaasSubscription::class, $result);
    }

    public function test_creates_subscription_with_custom_cycle()
    {
        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();
        DB::shouldReceive('rollback')->never();

        $subscriptionId = 100;

        // Create subscription domain
        $subscription = $this->createSubscription(999.90);

        $this->subscriptionRepository
            ->shouldReceive('findById')
            ->with($subscriptionId)
            ->once()
            ->andReturn($subscription);

        // Mock ASAAS API response for yearly cycle
        $asaasResponse = [
            'id' => 'sub_987654321',
            'customer' => 'cus_123456789',
            'dateCreated' => '2025-01-01',
            'billingType' => 'BOLETO',
            'cycle' => 'MONTHLY',
            'value' => 999.90,
            'nextDueDate' => '2025-02-01',
            'status' => 'ACTIVE',
            'description' => 'Assinatura do sistema',
        ];

        $this->asaasService
            ->shouldReceive('post')
            ->with('/v3/subscriptions', [
                'customer' => 1,
                'billingType' => 'BOLETO',
                'value' => 999.90,
                'nextDueDate' => '2025-12-31',
                'cycle' => 'MONTHLY',
                'description' => 'Assinatura do sistema',
            ])
            ->once()
            ->andReturn($asaasResponse);

        // Mock factory
        $asaasSubscription = Mockery::mock(AsaasSubscription::class);
        $asaasSubscription->shouldReceive('getAttribute')->with('id')->andReturn(1);
        $asaasSubscription->shouldReceive('getAttribute')->with('asaas_subscription_id')->andReturn('sub_987654321');

        $this->asaasSubscriptionFactory
            ->shouldReceive('buildFromStoreArray')
            ->once()
            ->andReturn($asaasSubscription);

        $this->asaasSubscriptionRepository
            ->shouldReceive('store')
            ->with($asaasSubscription)
            ->once()
            ->andReturn($asaasSubscription);

        $result = $this->useCase->perform($subscriptionId);

        $this->assertInstanceOf(AsaasSubscription::class, $result);
    }
}
