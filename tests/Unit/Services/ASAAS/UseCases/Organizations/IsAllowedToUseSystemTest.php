<?php

namespace Tests\Unit\Services\ASAAS\UseCases\Organizations;

use App\Domains\Organization as OrganizationDomain;
use App\Enums\AsaasEnvironment;
use App\Services\ASAAS\Domains\AsaasOrganization;
use App\Services\ASAAS\UseCases\Deprecated\Organizations\IsAllowedToUseSystem;
use Carbon\Carbon;
use Tests\TestCase;

class IsAllowedToUseSystemTest extends TestCase
{
    private IsAllowedToUseSystem $useCase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->useCase = new IsAllowedToUseSystem();
    }

    private function createOrganizationDomain(array $overrides = []): OrganizationDomain
    {
        $defaults = [
            'id' => 1,
            'name' => 'Test Organization',
            'description' => 'Test Description',
            'is_active' => true,
            'is_suspended' => false,
            'default_flow_id' => null,
            'created_at' => null,
            'updated_at' => null,
            'is_courtesy' => null,
            'courtesy_expires_at' => null,
            'courtesy_reason' => null,
            'asaas' => null,
        ];

        $data = array_merge($defaults, $overrides);

        return new OrganizationDomain(
            id: $data['id'],
            name: $data['name'],
            description: $data['description'],
            is_active: $data['is_active'],
            is_suspended: $data['is_suspended'],
            default_flow_id: $data['default_flow_id'],
            created_at: $data['created_at'],
            updated_at: $data['updated_at'],
            is_courtesy: $data['is_courtesy'],
            courtesy_expires_at: $data['courtesy_expires_at'],
            courtesy_reason: $data['courtesy_reason'],
            asaas: $data['asaas']
        );
    }

    private function createAsaasOrganization(array $overrides = []): AsaasOrganization
    {
        $defaults = [
            'id' => 1,
            'organization_id' => 1,
            'organization' => null,
            'asaas_account_id' => 'acc_123456',
            'asaas_api_key' => 'key_789',
            'asaas_wallet_id' => 'wallet_123',
            'asaas_environment' => AsaasEnvironment::SANDBOX,
            'is_active' => true,
            'last_sync_at' => null,
            'sync_errors' => null,
        ];

        $data = array_merge($defaults, $overrides);

        return new AsaasOrganization(
            id: $data['id'],
            organization_id: $data['organization_id'],
            organization: $data['organization'],
            asaas_account_id: $data['asaas_account_id'],
            asaas_api_key: $data['asaas_api_key'],
            asaas_wallet_id: $data['asaas_wallet_id'],
            asaas_environment: $data['asaas_environment'],
            is_active: $data['is_active'],
            last_sync_at: $data['last_sync_at'],
            sync_errors: $data['sync_errors']
        );
    }

    public function test_returns_not_allowed_when_organization_is_inactive()
    {
        $organization = $this->createOrganizationDomain([
            'is_active' => false
        ]);

        $result = $this->useCase->perform($organization);

        $this->assertFalse($result['allowed']);
        $this->assertEquals('organization_inactive', $result['reason']);
        $this->assertEquals('Organization is inactive', $result['message']);
    }

    public function test_returns_not_allowed_when_organization_is_suspended()
    {
        // Since suspension check is commented out in the use case,
        // suspended organizations will be checked for ASAAS integration instead
        $organization = $this->createOrganizationDomain([
            'is_suspended' => true,
            'asaas' => null // No ASAAS integration
        ]);

        $result = $this->useCase->perform($organization);

        $this->assertFalse($result['allowed']);
        $this->assertEquals('no_asaas_integration', $result['reason']);
        $this->assertEquals('Organization has no ASAAS integration', $result['message']);
    }

    public function test_returns_not_allowed_when_no_asaas_integration()
    {
        $organization = $this->createOrganizationDomain();

        $result = $this->useCase->perform($organization);

        $this->assertFalse($result['allowed']);
        $this->assertEquals('no_asaas_integration', $result['reason']);
        $this->assertEquals('Organization has no ASAAS integration', $result['message']);
    }

    public function test_returns_allowed_when_has_active_subscription()
    {
        $asaasOrganization = $this->createAsaasOrganization([
            'is_active' => true,
        ]);

        $organization = $this->createOrganizationDomain([
            'asaas' => $asaasOrganization
        ]);

        $result = $this->useCase->perform($organization);

        $this->assertTrue($result['allowed']);
        $this->assertEquals('subscription_active', $result['reason']);
        $this->assertEquals('Organization has active ASAAS account', $result['message']);
    }

    public function test_returns_allowed_when_in_courtesy_period()
    {
        $asaasOrganization = $this->createAsaasOrganization([
            'is_active' => false,
        ]);

        $organization = $this->createOrganizationDomain([
            'is_courtesy' => true,
            'courtesy_expires_at' => Carbon::now()->addDays(10),
            'asaas' => $asaasOrganization
        ]);

        $result = $this->useCase->perform($organization);

        $this->assertTrue($result['allowed']);
        $this->assertEquals('courtesy_active', $result['reason']);
        $this->assertStringContainsString('Courtesy access valid until', $result['message']);
    }

    public function test_returns_not_allowed_when_courtesy_expired()
    {
        $asaasOrganization = $this->createAsaasOrganization([
            'is_active' => false,
        ]);

        $organization = $this->createOrganizationDomain([
            'is_courtesy' => true,
            'courtesy_expires_at' => Carbon::now()->subDays(1), // Expired
            'asaas' => $asaasOrganization
        ]);

        $result = $this->useCase->perform($organization);

        $this->assertFalse($result['allowed']);
        $this->assertEquals('courtesy_expired', $result['reason']);
        $this->assertStringContainsString('Courtesy access expired on', $result['message']);
    }

    public function test_returns_not_allowed_when_subscription_expired()
    {
        $asaasOrganization = $this->createAsaasOrganization([
            'is_active' => false,
        ]);

        $organization = $this->createOrganizationDomain([
            'asaas' => $asaasOrganization
        ]);

        $result = $this->useCase->perform($organization);

        $this->assertFalse($result['allowed']);
        $this->assertEquals('subscription_inactive', $result['reason']);
        $this->assertEquals('ASAAS account is inactive', $result['message']);
    }

    public function test_returns_not_allowed_when_subscription_inactive()
    {
        $asaasOrganization = $this->createAsaasOrganization([
            'is_active' => false,
        ]);

        $organization = $this->createOrganizationDomain([
            'asaas' => $asaasOrganization
        ]);

        $result = $this->useCase->perform($organization);

        $this->assertFalse($result['allowed']);
        $this->assertEquals('subscription_inactive', $result['reason']);
        $this->assertEquals('ASAAS account is inactive', $result['message']);
    }

    public function test_returns_not_allowed_when_subscription_overdue()
    {
        $asaasOrganization = $this->createAsaasOrganization([
            'is_active' => false,
        ]);

        $organization = $this->createOrganizationDomain([
            'asaas' => $asaasOrganization
        ]);

        $result = $this->useCase->perform($organization);

        $this->assertFalse($result['allowed']);
        $this->assertEquals('subscription_inactive', $result['reason']);
        $this->assertEquals('ASAAS account is inactive', $result['message']);
    }

    public function test_result_includes_organization_details()
    {
        $asaasOrganization = $this->createAsaasOrganization([
            'is_active' => true,
        ]);

        $organization = $this->createOrganizationDomain([
            'id' => 123,
            'name' => 'Test Org',
            'asaas' => $asaasOrganization
        ]);

        $result = $this->useCase->perform($organization);

        $this->assertTrue($result['allowed']);
        $this->assertEquals('subscription_active', $result['reason']);
        $this->assertArrayHasKey('details', $result);
    }

    public function test_result_includes_next_action_when_not_allowed()
    {
        $organization = $this->createOrganizationDomain([
            'is_active' => false
        ]);

        $result = $this->useCase->perform($organization);

        $this->assertFalse($result['allowed']);
        $this->assertEquals('organization_inactive', $result['reason']);
    }

    public function test_result_includes_null_next_action_when_allowed()
    {
        $asaasOrganization = $this->createAsaasOrganization([
            'is_active' => true,
        ]);

        $organization = $this->createOrganizationDomain([
            'asaas' => $asaasOrganization
        ]);

        $result = $this->useCase->perform($organization);

        $this->assertTrue($result['allowed']);
        $this->assertEquals('subscription_active', $result['reason']);
    }

    public function test_handles_organization_without_asaas_relationship()
    {
        $organization = $this->createOrganizationDomain([
            'asaas' => null
        ]);

        $result = $this->useCase->perform($organization);

        $this->assertFalse($result['allowed']);
        $this->assertEquals('no_asaas_integration', $result['reason']);
        $this->assertFalse($result['details']['has_asaas_account']);
    }
}
