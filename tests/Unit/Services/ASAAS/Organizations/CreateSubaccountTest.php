<?php

namespace Tests\Unit\Services\ASAAS\Organizations;

use App\Models\Organization;
use App\Services\ASAAS\AsaasService;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Services\ASAAS\Factories\AsaasOrganizationFactory;
use App\Services\ASAAS\Repositories\AsaasOrganizationRepository;
use App\Services\ASAAS\UseCases\Deprecated\Organizations\CreateSubaccount;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class CreateSubaccountTest extends TestCase
{
    use RefreshDatabase;

    protected CreateSubaccount $createSubaccount;
    protected $mockAsaasService;
    protected $mockRepository;
    protected $mockFactory;
    protected Organization $organization;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockAsaasService = Mockery::mock(AsaasService::class);
        $this->mockRepository = Mockery::mock(AsaasOrganizationRepository::class);
        $this->mockFactory = Mockery::mock(AsaasOrganizationFactory::class);

        $this->createSubaccount = new CreateSubaccount(
            $this->mockAsaasService,
            $this->mockRepository,
            $this->mockFactory
        );

        $this->organization = Organization::factory()->create([
            'name' => 'Test Organization',
        ]);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_can_instantiate_create_subaccount()
    {
        $this->assertInstanceOf(CreateSubaccount::class, $this->createSubaccount);
    }

    public function test_can_mock_asaas_service()
    {
        $this->mockAsaasService
            ->shouldReceive('post')
            ->never(); // Don't expect any calls in this test

        $this->assertTrue(true); // Mock setup successful
    }

    public function test_can_handle_asaas_exception()
    {
        $asaasException = new AsaasException(
            'ASAAS API Error: Invalid email',
            400,
            null,
            null,
            ['errors' => [['code' => 'invalid_email', 'description' => 'Invalid email']]],
            'invalid_email'
        );

        $this->assertInstanceOf(AsaasException::class, $asaasException);
        $this->assertEquals('ASAAS API Error: Invalid email', $asaasException->getMessage());
        $this->assertEquals(400, $asaasException->getCode());
        $this->assertEquals('invalid_email', $asaasException->getAsaasErrorCode());
    }
}
